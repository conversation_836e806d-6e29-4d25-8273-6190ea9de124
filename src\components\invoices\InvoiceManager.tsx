import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Plus, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Edit, 
  Trash2,
  ShoppingCart,
  ShoppingBag,
  RotateCcw,
  FileText,
  Calendar,
  DollarSign
} from 'lucide-react';
import { Invoice, InvoiceType, PaymentStatus, Customer, Supplier, Product } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { toast } from '@/hooks/use-toast';
import SalesInvoice from './SalesInvoice';
import PurchaseInvoice from './PurchaseInvoice';
import ReturnInvoice from './ReturnInvoice';

interface InvoiceManagerProps {
  invoices: Invoice[];
  customers: Customer[];
  suppliers: Supplier[];
  products: Product[];
  onCreateInvoice: (invoice: Partial<Invoice>) => Promise<void>;
  onUpdateInvoice: (id: string, invoice: Partial<Invoice>) => Promise<void>;
  onDeleteInvoice: (id: string) => Promise<void>;
  onSearchInvoice?: (invoiceNumber: string) => Promise<Invoice | null>;
  onSuspendInvoice?: (invoice: Partial<Invoice>) => Promise<void>;
  onDownloadPDF?: (invoice: Invoice) => Promise<void>;
}

const InvoiceManager: React.FC<InvoiceManagerProps> = ({
  invoices,
  customers,
  suppliers,
  products,
  onCreateInvoice,
  onUpdateInvoice,
  onDeleteInvoice,
  onSearchInvoice,
  onSuspendInvoice,
  onDownloadPDF
}) => {
  const [activeTab, setActiveTab] = useState('list');
  const [selectedInvoiceType, setSelectedInvoiceType] = useState<InvoiceType>('sales');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<InvoiceType | ''>('');
  const [filterStatus, setFilterStatus] = useState<PaymentStatus | ''>('');
  const [filterDateFrom, setFilterDateFrom] = useState('');
  const [filterDateTo, setFilterDateTo] = useState('');
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // تصفية الفواتير
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = !searchTerm || 
      invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customer?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.supplier?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (invoice.notes && invoice.notes.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = !filterType || invoice.type === filterType;
    const matchesStatus = !filterStatus || invoice.payment_status === filterStatus;
    
    const matchesDateFrom = !filterDateFrom || 
      new Date(invoice.invoice_date) >= new Date(filterDateFrom);
    const matchesDateTo = !filterDateTo || 
      new Date(invoice.invoice_date) <= new Date(filterDateTo);

    return matchesSearch && matchesType && matchesStatus && matchesDateFrom && matchesDateTo;
  });

  // إحصائيات سريعة
  const stats = {
    total: invoices.length,
    sales: invoices.filter(i => i.type === 'sales').length,
    purchases: invoices.filter(i => i.type === 'purchase').length,
    returns: invoices.filter(i => i.type.includes('return')).length,
    totalAmount: invoices.reduce((sum, i) => sum + i.total_amount, 0),
    paidAmount: invoices.reduce((sum, i) => sum + i.paid_amount, 0),
    pendingAmount: invoices.reduce((sum, i) => sum + i.remaining_amount, 0)
  };

  const handleCreateInvoice = async (invoiceData: Partial<Invoice>) => {
    try {
      await onCreateInvoice(invoiceData);
      setIsCreateDialogOpen(false);
      setActiveTab('list');
      toast({
        title: "تم الإنشاء",
        description: "تم إنشاء الفاتورة بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في إنشاء الفاتورة",
        variant: "destructive"
      });
    }
  };

  const handleUpdateInvoice = async (invoiceData: Partial<Invoice>) => {
    if (!selectedInvoice) return;
    
    try {
      await onUpdateInvoice(selectedInvoice.id, invoiceData);
      setIsEditDialogOpen(false);
      setSelectedInvoice(null);
      toast({
        title: "تم التحديث",
        description: "تم تحديث الفاتورة بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث الفاتورة",
        variant: "destructive"
      });
    }
  };

  const handleDeleteInvoice = async (invoice: Invoice) => {
    if (!confirm(`هل أنت متأكد من حذف الفاتورة ${invoice.invoice_number}؟`)) {
      return;
    }

    try {
      await onDeleteInvoice(invoice.id);
      toast({
        title: "تم الحذف",
        description: "تم حذف الفاتورة بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في حذف الفاتورة",
        variant: "destructive"
      });
    }
  };

  const getInvoiceTypeIcon = (type: InvoiceType) => {
    switch (type) {
      case 'sales':
        return <ShoppingBag className="w-4 h-4" />;
      case 'purchase':
        return <ShoppingCart className="w-4 h-4" />;
      case 'sales_return':
      case 'purchase_return':
        return <RotateCcw className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getInvoiceTypeLabel = (type: InvoiceType) => {
    switch (type) {
      case 'sales':
        return 'بيع';
      case 'purchase':
        return 'شراء';
      case 'sales_return':
        return 'إرجاع بيع';
      case 'purchase_return':
        return 'إرجاع شراء';
      default:
        return type;
    }
  };

  const getStatusBadge = (status: PaymentStatus) => {
    const variants = {
      paid: 'default',
      partial: 'secondary',
      unpaid: 'destructive'
    } as const;

    const labels = {
      paid: 'مدفوع',
      partial: 'جزئي',
      unpaid: 'غير مدفوع'
    };

    return (
      <Badge variant={variants[status]}>
        {labels[status]}
      </Badge>
    );
  };

  return (
    <div className="space-y-6 p-6" dir="rtl">
      {/* الإحصائيات السريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الفواتير</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <FileText className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">فواتير البيع</p>
                <p className="text-2xl font-bold text-green-600">{stats.sales}</p>
              </div>
              <ShoppingBag className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">فواتير الشراء</p>
                <p className="text-2xl font-bold text-orange-600">{stats.purchases}</p>
              </div>
              <ShoppingCart className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المبالغ</p>
                <p className="text-2xl font-bold">{formatCurrency(stats.totalAmount)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* التبويبات الرئيسية */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="list">قائمة الفواتير</TabsTrigger>
          <TabsTrigger value="sales">فاتورة بيع</TabsTrigger>
          <TabsTrigger value="purchase">فاتورة شراء</TabsTrigger>
          <TabsTrigger value="sales_return">إرجاع بيع</TabsTrigger>
          <TabsTrigger value="purchase_return">إرجاع شراء</TabsTrigger>
        </TabsList>

        {/* قائمة الفواتير */}
        <TabsContent value="list" className="space-y-4">
          {/* أدوات البحث والتصفية */}
          <Card>
            <CardHeader>
              <CardTitle>البحث والتصفية</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                  <Label>البحث</Label>
                  <div className="relative">
                    <Search className="absolute right-3 top-3 w-4 h-4 text-gray-400" />
                    <Input
                      placeholder="رقم الفاتورة، العميل، المورد..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pr-10"
                    />
                  </div>
                </div>
                
                <div>
                  <Label>نوع الفاتورة</Label>
                  <Select value={filterType} onValueChange={(value) => setFilterType(value as InvoiceType | '')}>
                    <SelectTrigger>
                      <SelectValue placeholder="جميع الأنواع" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">جميع الأنواع</SelectItem>
                      <SelectItem value="sales">بيع</SelectItem>
                      <SelectItem value="purchase">شراء</SelectItem>
                      <SelectItem value="sales_return">إرجاع بيع</SelectItem>
                      <SelectItem value="purchase_return">إرجاع شراء</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>حالة الدفع</Label>
                  <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value as PaymentStatus | '')}>
                    <SelectTrigger>
                      <SelectValue placeholder="جميع الحالات" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">جميع الحالات</SelectItem>
                      <SelectItem value="paid">مدفوع</SelectItem>
                      <SelectItem value="partial">جزئي</SelectItem>
                      <SelectItem value="unpaid">غير مدفوع</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>من تاريخ</Label>
                  <Input
                    type="date"
                    value={filterDateFrom}
                    onChange={(e) => setFilterDateFrom(e.target.value)}
                  />
                </div>
                
                <div>
                  <Label>إلى تاريخ</Label>
                  <Input
                    type="date"
                    value={filterDateTo}
                    onChange={(e) => setFilterDateTo(e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* جدول الفواتير */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>الفواتير ({filteredInvoices.length})</span>
                <div className="flex gap-2">
                  <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="w-4 h-4 ml-2" />
                        فاتورة جديدة
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>إنشاء فاتورة جديدة</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label>نوع الفاتورة</Label>
                          <Select value={selectedInvoiceType} onValueChange={(value) => setSelectedInvoiceType(value as InvoiceType)}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="sales">فاتورة بيع</SelectItem>
                              <SelectItem value="purchase">فاتورة شراء</SelectItem>
                              <SelectItem value="sales_return">إرجاع بيع</SelectItem>
                              <SelectItem value="purchase_return">إرجاع شراء</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {selectedInvoiceType === 'sales' && (
                          <SalesInvoice
                            customers={customers}
                            products={products}
                            onSave={handleCreateInvoice}
                            onSuspend={onSuspendInvoice}
                          />
                        )}

                        {selectedInvoiceType === 'purchase' && (
                          <PurchaseInvoice
                            suppliers={suppliers}
                            products={products}
                            onSave={handleCreateInvoice}
                          />
                        )}

                        {selectedInvoiceType === 'sales_return' && (
                          <ReturnInvoice
                            returnType="sales_return"
                            customers={customers}
                            onSave={handleCreateInvoice}
                            onSearchOriginalInvoice={onSearchInvoice}
                          />
                        )}

                        {selectedInvoiceType === 'purchase_return' && (
                          <ReturnInvoice
                            returnType="purchase_return"
                            suppliers={suppliers}
                            onSave={handleCreateInvoice}
                            onSearchOriginalInvoice={onSearchInvoice}
                          />
                        )}
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3">رقم الفاتورة</th>
                      <th className="text-right p-3">النوع</th>
                      <th className="text-right p-3">العميل/المورد</th>
                      <th className="text-right p-3">التاريخ</th>
                      <th className="text-right p-3">المجموع</th>
                      <th className="text-right p-3">المدفوع</th>
                      <th className="text-right p-3">المتبقي</th>
                      <th className="text-right p-3">الحالة</th>
                      <th className="text-right p-3">إجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredInvoices.map((invoice) => (
                      <tr key={invoice.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium">{invoice.invoice_number}</td>
                        <td className="p-3">
                          <div className="flex items-center gap-2">
                            {getInvoiceTypeIcon(invoice.type)}
                            <span>{getInvoiceTypeLabel(invoice.type)}</span>
                          </div>
                        </td>
                        <td className="p-3">
                          {invoice.customer?.name || invoice.supplier?.name || 'غير محدد'}
                        </td>
                        <td className="p-3">
                          {new Date(invoice.invoice_date).toLocaleDateString('ar-SA')}
                        </td>
                        <td className="p-3 font-semibold">
                          {formatCurrency(invoice.total_amount)}
                        </td>
                        <td className="p-3 text-green-600">
                          {formatCurrency(invoice.paid_amount)}
                        </td>
                        <td className="p-3 text-red-600">
                          {formatCurrency(invoice.remaining_amount)}
                        </td>
                        <td className="p-3">
                          {getStatusBadge(invoice.payment_status)}
                        </td>
                        <td className="p-3">
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedInvoice(invoice);
                                setIsEditDialogOpen(true);
                              }}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>

                            {onDownloadPDF && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onDownloadPDF(invoice)}
                              >
                                <Download className="w-4 h-4" />
                              </Button>
                            )}

                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleDeleteInvoice(invoice)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {filteredInvoices.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد فواتير تطابق معايير البحث
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* تبويبات إنشاء الفواتير */}
        <TabsContent value="sales">
          <SalesInvoice
            customers={customers}
            products={products}
            onSave={handleCreateInvoice}
            onSuspend={onSuspendInvoice}
          />
        </TabsContent>

        <TabsContent value="purchase">
          <PurchaseInvoice
            suppliers={suppliers}
            products={products}
            onSave={handleCreateInvoice}
          />
        </TabsContent>

        <TabsContent value="sales_return">
          <ReturnInvoice
            returnType="sales_return"
            customers={customers}
            onSave={handleCreateInvoice}
            onSearchOriginalInvoice={onSearchInvoice}
          />
        </TabsContent>

        <TabsContent value="purchase_return">
          <ReturnInvoice
            returnType="purchase_return"
            suppliers={suppliers}
            onSave={handleCreateInvoice}
            onSearchOriginalInvoice={onSearchInvoice}
          />
        </TabsContent>
      </Tabs>

      {/* نافذة تعديل الفاتورة */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>تعديل الفاتورة {selectedInvoice?.invoice_number}</DialogTitle>
          </DialogHeader>
          {selectedInvoice && (
            <div>
              {selectedInvoice.type === 'sales' && (
                <SalesInvoice
                  invoice={selectedInvoice}
                  customers={customers}
                  products={products}
                  onSave={handleUpdateInvoice}
                />
              )}

              {selectedInvoice.type === 'purchase' && (
                <PurchaseInvoice
                  invoice={selectedInvoice}
                  suppliers={suppliers}
                  products={products}
                  onSave={handleUpdateInvoice}
                />
              )}

              {(selectedInvoice.type === 'sales_return' || selectedInvoice.type === 'purchase_return') && (
                <ReturnInvoice
                  returnType={selectedInvoice.type}
                  invoice={selectedInvoice}
                  customers={customers}
                  suppliers={suppliers}
                  onSave={handleUpdateInvoice}
                  onSearchOriginalInvoice={onSearchInvoice}
                />
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InvoiceManager;
