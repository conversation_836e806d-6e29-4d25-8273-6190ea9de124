import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Calculator, CreditCard, Banknote, Clock, CheckCircle } from 'lucide-react';
import { PaymentMethod, PaymentStatus } from '@/types';
import { formatCurrency } from '@/utils/currency';

interface PaymentCalculatorProps {
  totalAmount: number;
  currentPaidAmount?: number;
  onPaymentChange: (payment: PaymentCalculation) => void;
  allowCredit?: boolean;
  allowMixed?: boolean;
}

export interface PaymentCalculation {
  cashAmount: number;
  cardAmount: number;
  creditAmount: number;
  totalPaid: number;
  remainingAmount: number;
  changeAmount: number;
  paymentMethod: PaymentMethod;
  paymentStatus: PaymentStatus;
}

const PaymentCalculator: React.FC<PaymentCalculatorProps> = ({
  totalAmount,
  currentPaidAmount = 0,
  onPaymentChange,
  allowCredit = true,
  allowMixed = true
}) => {
  const [cashAmount, setCashAmount] = useState(0);
  const [cardAmount, setCardAmount] = useState(0);
  const [creditAmount, setCreditAmount] = useState(0);
  const [receivedCash, setReceivedCash] = useState(0);

  useEffect(() => {
    calculatePayment();
  }, [cashAmount, cardAmount, creditAmount, receivedCash, totalAmount]);

  const calculatePayment = () => {
    const totalPaid = cashAmount + cardAmount + creditAmount;
    const remainingAmount = Math.max(0, totalAmount - totalPaid);
    const changeAmount = Math.max(0, receivedCash - cashAmount);

    let paymentMethod: PaymentMethod = 'cash';
    let paymentStatus: PaymentStatus = 'unpaid';

    // تحديد طريقة الدفع
    const paymentMethods = [
      { amount: cashAmount, method: 'cash' as PaymentMethod },
      { amount: cardAmount, method: 'card' as PaymentMethod },
      { amount: creditAmount, method: 'credit' as PaymentMethod }
    ].filter(p => p.amount > 0);

    if (paymentMethods.length > 1) {
      paymentMethod = 'mixed';
    } else if (paymentMethods.length === 1) {
      paymentMethod = paymentMethods[0].method;
    }

    // تحديد حالة الدفع
    if (totalPaid >= totalAmount) {
      paymentStatus = 'paid';
    } else if (totalPaid > 0) {
      paymentStatus = 'partial';
    }

    const calculation: PaymentCalculation = {
      cashAmount,
      cardAmount,
      creditAmount,
      totalPaid,
      remainingAmount,
      changeAmount,
      paymentMethod,
      paymentStatus
    };

    onPaymentChange(calculation);
  };

  const handleQuickPayment = (method: PaymentMethod) => {
    const remaining = totalAmount - (cashAmount + cardAmount + creditAmount);
    
    switch (method) {
      case 'cash':
        setCashAmount(prev => prev + remaining);
        setReceivedCash(cashAmount + remaining);
        break;
      case 'card':
        setCardAmount(prev => prev + remaining);
        break;
      case 'credit':
        setCreditAmount(prev => prev + remaining);
        break;
    }
  };

  const handleFullCash = () => {
    setCashAmount(totalAmount);
    setCardAmount(0);
    setCreditAmount(0);
    setReceivedCash(totalAmount);
  };

  const handleFullCard = () => {
    setCashAmount(0);
    setCardAmount(totalAmount);
    setCreditAmount(0);
    setReceivedCash(0);
  };

  const handleFullCredit = () => {
    setCashAmount(0);
    setCardAmount(0);
    setCreditAmount(totalAmount);
    setReceivedCash(0);
  };

  const clearPayment = () => {
    setCashAmount(0);
    setCardAmount(0);
    setCreditAmount(0);
    setReceivedCash(0);
  };

  const totalPaid = cashAmount + cardAmount + creditAmount;
  const remainingAmount = Math.max(0, totalAmount - totalPaid);
  const changeAmount = Math.max(0, receivedCash - cashAmount);
  const isFullyPaid = totalPaid >= totalAmount;

  return (
    <Card className="w-full" dir="rtl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="w-5 h-5" />
          حاسبة الدفع
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* ملخص المبلغ */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <div className="flex justify-between items-center text-lg font-bold">
            <span>إجمالي الفاتورة:</span>
            <span className="text-blue-600">{formatCurrency(totalAmount)}</span>
          </div>
        </div>

        {/* أزرار الدفع السريع */}
        <div className="grid grid-cols-3 gap-2">
          <Button
            variant="outline"
            onClick={handleFullCash}
            className="flex items-center gap-2"
          >
            <Banknote className="w-4 h-4" />
            نقداً كاملاً
          </Button>
          
          <Button
            variant="outline"
            onClick={handleFullCard}
            className="flex items-center gap-2"
          >
            <CreditCard className="w-4 h-4" />
            بطاقة كاملاً
          </Button>
          
          {allowCredit && (
            <Button
              variant="outline"
              onClick={handleFullCredit}
              className="flex items-center gap-2"
            >
              <Clock className="w-4 h-4" />
              آجل كاملاً
            </Button>
          )}
        </div>

        <Separator />

        {/* تفاصيل الدفع */}
        <div className="space-y-4">
          {/* الدفع النقدي */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Banknote className="w-4 h-4" />
              الدفع النقدي
            </Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-sm">المبلغ المطلوب</Label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={cashAmount}
                  onChange={(e) => setCashAmount(parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                />
              </div>
              <div>
                <Label className="text-sm">المبلغ المستلم</Label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={receivedCash}
                  onChange={(e) => setReceivedCash(parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                />
              </div>
            </div>
            {changeAmount > 0 && (
              <div className="p-2 bg-green-50 rounded text-green-700 text-sm">
                الباقي للعميل: {formatCurrency(changeAmount)}
              </div>
            )}
          </div>

          {/* الدفع بالبطاقة */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <CreditCard className="w-4 h-4" />
              الدفع بالبطاقة
            </Label>
            <Input
              type="number"
              min="0"
              step="0.01"
              value={cardAmount}
              onChange={(e) => setCardAmount(parseFloat(e.target.value) || 0)}
              placeholder="0.00"
            />
          </div>

          {/* الدفع الآجل */}
          {allowCredit && (
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                الدفع الآجل (دين)
              </Label>
              <Input
                type="number"
                min="0"
                step="0.01"
                value={creditAmount}
                onChange={(e) => setCreditAmount(parseFloat(e.target.value) || 0)}
                placeholder="0.00"
              />
            </div>
          )}
        </div>

        <Separator />

        {/* ملخص الدفع */}
        <div className="space-y-3">
          <div className="flex justify-between">
            <span>إجمالي المدفوع:</span>
            <span className="font-semibold text-green-600">
              {formatCurrency(totalPaid)}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span>المتبقي:</span>
            <span className={`font-semibold ${remainingAmount > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {formatCurrency(remainingAmount)}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <span>حالة الدفع:</span>
            <Badge variant={isFullyPaid ? 'default' : totalPaid > 0 ? 'secondary' : 'destructive'}>
              {isFullyPaid ? (
                <span className="flex items-center gap-1">
                  <CheckCircle className="w-3 h-3" />
                  مدفوع بالكامل
                </span>
              ) : totalPaid > 0 ? 'مدفوع جزئياً' : 'غير مدفوع'}
            </Badge>
          </div>

          {totalPaid > 0 && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm space-y-1">
                <div className="font-medium">تفاصيل الدفع:</div>
                {cashAmount > 0 && (
                  <div className="flex justify-between">
                    <span>نقداً:</span>
                    <span>{formatCurrency(cashAmount)}</span>
                  </div>
                )}
                {cardAmount > 0 && (
                  <div className="flex justify-between">
                    <span>بطاقة:</span>
                    <span>{formatCurrency(cardAmount)}</span>
                  </div>
                )}
                {creditAmount > 0 && (
                  <div className="flex justify-between">
                    <span>آجل:</span>
                    <span>{formatCurrency(creditAmount)}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={clearPayment}
            className="flex-1"
          >
            مسح الكل
          </Button>
          
          {remainingAmount > 0 && (
            <Button
              onClick={() => handleQuickPayment('cash')}
              className="flex-1"
            >
              دفع الباقي نقداً
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PaymentCalculator;
