import React, { useState } from 'react';
import { Layout } from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  FileText, 
  ShoppingCart, 
  Package, 
  RotateCcw, 
  Search,
  BarChart3,
  Settings,
  Users,
  Truck
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

// استيراد المكونات الجديدة
import InvoiceManager from "@/components/invoices/InvoiceManager";
import SmartSearch from "@/components/search/SmartSearch";
import IntelligentReports from "@/components/reports/IntelligentReports";
import SuspendedInvoices from "@/components/invoices/SuspendedInvoices";
import { Invoice, Customer, Supplier, Product, User, SuspendedInvoice } from "@/types";
import { formatCurrency } from "@/utils/currency";

const InvoicesNew = () => {
  const [activeTab, setActiveTab] = useState('invoices');
  const [searchResults, setSearchResults] = useState<Invoice[]>([]);
  const { toast } = useToast();

  // جلب البيانات من قاعدة البيانات
  const { data: invoices = [], isLoading: invoicesLoading, refetch: refetchInvoices } = useQuery({
    queryKey: ['invoices'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('invoices')
        .select(`
          *,
          customers (id, name, phone, address),
          suppliers (id, name, phone, address),
          invoice_items (
            id,
            product_id,
            product_name,
            quantity,
            unit_price,
            discount_amount,
            discount_percent,
            tax_amount,
            tax_percent,
            total_price,
            notes
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Invoice[];
    }
  });

  const { data: customers = [] } = useQuery({
    queryKey: ['customers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .order('name');
      if (error) throw error;
      return data.map(customer => ({
        ...customer,
        balance: 0,
        totalPurchases: 0,
        createdAt: new Date(customer.created_at)
      })) as Customer[];
    }
  });

  const { data: suppliers = [] } = useQuery({
    queryKey: ['suppliers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('suppliers')
        .select('*')
        .order('name');
      if (error) throw error;
      return data.map(supplier => ({
        ...supplier,
        balance: 0,
        totalPurchases: 0,
        createdAt: new Date(supplier.created_at)
      })) as Supplier[];
    }
  });

  const { data: products = [] } = useQuery({
    queryKey: ['products'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .order('name');
      if (error) throw error;
      return data.map(product => ({
        id: product.id,
        name: product.name,
        nameAr: product.name,
        category: product.category || 'عام',
        brand: product.brand || '',
        barcode: product.barcode || '',
        unit: product.unit || 'قطعة',
        purchasePrice: product.purchase_price || 0,
        salePrice: product.selling_price || 0,
        stock: product.stock_quantity || 0,
        minStock: product.min_stock_level || 0,
        description: product.description,
        image: product.image_url,
        createdAt: new Date(product.created_at),
        updatedAt: new Date(product.updated_at)
      })) as Product[];
    }
  });

  const { data: suspendedInvoices = [] } = useQuery({
    queryKey: ['suspended-invoices'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('suspended_invoices')
        .select('*')
        .eq('is_active', true)
        .order('suspended_at', { ascending: false });
      if (error) throw error;
      return data as SuspendedInvoice[];
    }
  });

  // معالجات الأحداث
  const handleCreateInvoice = async (invoiceData: any) => {
    try {
      // إنشاء الفاتورة في قاعدة البيانات
      const { data: invoice, error: invoiceError } = await supabase
        .from('invoices')
        .insert([invoiceData])
        .select()
        .single();

      if (invoiceError) throw invoiceError;

      // إنشاء عناصر الفاتورة
      if (invoiceData.items && invoiceData.items.length > 0) {
        const items = invoiceData.items.map((item: any) => ({
          ...item,
          invoice_id: invoice.id
        }));

        const { error: itemsError } = await supabase
          .from('invoice_items')
          .insert(items);

        if (itemsError) throw itemsError;
      }

      // تحديث المخزون للمنتجات
      if (invoiceData.type === 'sales' || invoiceData.type === 'sales_return') {
        for (const item of invoiceData.items || []) {
          const product = products.find(p => p.id === item.product_id);
          if (product) {
            const newStock = invoiceData.type === 'sales' 
              ? product.stock - item.quantity
              : product.stock + item.quantity;

            await supabase
              .from('products')
              .update({ stock_quantity: newStock })
              .eq('id', item.product_id);
          }
        }
      }

      refetchInvoices();
      toast({
        title: "تم الإنشاء",
        description: "تم إنشاء الفاتورة بنجاح"
      });
    } catch (error) {
      console.error('Error creating invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في إنشاء الفاتورة",
        variant: "destructive"
      });
    }
  };

  const handleUpdateInvoice = async (id: string, invoiceData: any) => {
    try {
      const { error } = await supabase
        .from('invoices')
        .update(invoiceData)
        .eq('id', id);

      if (error) throw error;

      refetchInvoices();
      toast({
        title: "تم التحديث",
        description: "تم تحديث الفاتورة بنجاح"
      });
    } catch (error) {
      console.error('Error updating invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحديث الفاتورة",
        variant: "destructive"
      });
    }
  };

  const handleDeleteInvoice = async (id: string) => {
    try {
      // حذف عناصر الفاتورة أولاً
      await supabase.from('invoice_items').delete().eq('invoice_id', id);
      
      // ثم حذف الفاتورة
      const { error } = await supabase.from('invoices').delete().eq('id', id);
      if (error) throw error;

      refetchInvoices();
      toast({
        title: "تم الحذف",
        description: "تم حذف الفاتورة بنجاح"
      });
    } catch (error) {
      console.error('Error deleting invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في حذف الفاتورة",
        variant: "destructive"
      });
    }
  };

  const handleSuspendInvoice = async (invoiceData: any) => {
    try {
      const { error } = await supabase
        .from('suspended_invoices')
        .insert([{
          invoice_data: invoiceData,
          customer_id: invoiceData.customer_id,
          supplier_id: invoiceData.supplier_id,
          suspended_by: 'current_user', // يجب استبداله بالمستخدم الحالي
          suspended_at: new Date().toISOString(),
          notes: invoiceData.notes,
          is_active: true
        }]);

      if (error) throw error;

      toast({
        title: "تم التعليق",
        description: "تم تعليق الفاتورة بنجاح"
      });
    } catch (error) {
      console.error('Error suspending invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في تعليق الفاتورة",
        variant: "destructive"
      });
    }
  };

  const handleRestoreSuspendedInvoice = async (suspendedInvoice: SuspendedInvoice) => {
    try {
      // إنشاء الفاتورة من البيانات المعلقة
      await handleCreateInvoice(suspendedInvoice.invoice_data);
      
      // إلغاء تفعيل الفاتورة المعلقة
      await supabase
        .from('suspended_invoices')
        .update({ is_active: false })
        .eq('id', suspendedInvoice.id);

      toast({
        title: "تم الاستعادة",
        description: "تم استعادة الفاتورة المعلقة بنجاح"
      });
    } catch (error) {
      console.error('Error restoring suspended invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في استعادة الفاتورة المعلقة",
        variant: "destructive"
      });
    }
  };

  const handleDeleteSuspendedInvoice = async (id: string) => {
    try {
      const { error } = await supabase
        .from('suspended_invoices')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast({
        title: "تم الحذف",
        description: "تم حذف الفاتورة المعلقة نهائياً"
      });
    } catch (error) {
      console.error('Error deleting suspended invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في حذف الفاتورة المعلقة",
        variant: "destructive"
      });
    }
  };

  const handleSearchInvoice = async (criteria: any) => {
    // البحث في الفواتير الموجودة
    const results = invoices.filter(invoice => {
      if (criteria.invoice_number) {
        return invoice.invoice_number.includes(criteria.invoice_number);
      }
      return true;
    });
    return results;
  };

  const handleDownloadPDF = async (invoice: Invoice) => {
    try {
      const { downloadInvoicePDF } = await import('@/utils/pdf-generator');
      await downloadInvoicePDF(invoice);
      toast({
        title: "تم التحميل",
        description: "تم تحميل ملف PDF بنجاح"
      });
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل ملف PDF",
        variant: "destructive"
      });
    }
  };

  // حساب الإحصائيات
  const stats = {
    totalInvoices: invoices.length,
    totalSales: invoices.filter(i => i.type === 'sales').reduce((sum, i) => sum + i.total_amount, 0),
    totalPurchases: invoices.filter(i => i.type === 'purchase').reduce((sum, i) => sum + i.total_amount, 0),
    pendingAmount: invoices.reduce((sum, i) => sum + i.remaining_amount, 0)
  };

  if (invoicesLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">جاري التحميل...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6" dir="rtl">
        {/* رأس الصفحة */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">نظام إدارة الفواتير المتقدم</h1>
            <p className="text-gray-600 mt-1">إدارة شاملة لجميع أنواع الفواتير والمدفوعات</p>
          </div>
        </div>

        {/* الإحصائيات السريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-600 text-sm font-medium">إجمالي الفواتير</p>
                  <p className="text-2xl font-bold text-blue-900">{stats.totalInvoices}</p>
                </div>
                <FileText className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-600 text-sm font-medium">إجمالي المبيعات</p>
                  <p className="text-2xl font-bold text-green-900">{formatCurrency(stats.totalSales)}</p>
                </div>
                <ShoppingCart className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-600 text-sm font-medium">إجمالي المشتريات</p>
                  <p className="text-2xl font-bold text-orange-900">{formatCurrency(stats.totalPurchases)}</p>
                </div>
                <Package className="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-600 text-sm font-medium">المبالغ المعلقة</p>
                  <p className="text-2xl font-bold text-red-900">{formatCurrency(stats.pendingAmount)}</p>
                </div>
                <RotateCcw className="w-8 h-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* التبويبات الرئيسية */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="invoices" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              إدارة الفواتير
            </TabsTrigger>
            <TabsTrigger value="search" className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              البحث الذكي
            </TabsTrigger>
            <TabsTrigger value="reports" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              التقارير
            </TabsTrigger>
            <TabsTrigger value="suspended" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              الفواتير المعلقة
            </TabsTrigger>
          </TabsList>

          {/* تبويب إدارة الفواتير */}
          <TabsContent value="invoices">
            <InvoiceManager
              invoices={searchResults.length > 0 ? searchResults : invoices}
              customers={customers}
              suppliers={suppliers}
              products={products}
              onCreateInvoice={handleCreateInvoice}
              onUpdateInvoice={handleUpdateInvoice}
              onDeleteInvoice={handleDeleteInvoice}
              onSuspendInvoice={handleSuspendInvoice}
              onDownloadPDF={handleDownloadPDF}
              onSearchInvoice={handleSearchInvoice}
            />
          </TabsContent>

          {/* تبويب البحث الذكي */}
          <TabsContent value="search">
            <SmartSearch
              invoices={invoices}
              customers={customers}
              suppliers={suppliers}
              products={products}
              onSearchResults={setSearchResults}
            />
          </TabsContent>

          {/* تبويب التقارير */}
          <TabsContent value="reports">
            <IntelligentReports
              invoices={invoices}
              customers={customers}
              suppliers={suppliers}
              products={products}
            />
          </TabsContent>

          {/* تبويب الفواتير المعلقة */}
          <TabsContent value="suspended">
            <SuspendedInvoices
              suspendedInvoices={suspendedInvoices}
              customers={customers}
              suppliers={suppliers}
              onRestoreInvoice={handleRestoreSuspendedInvoice}
              onDeleteSuspendedInvoice={handleDeleteSuspendedInvoice}
              onViewInvoiceData={(data) => console.log('View invoice data:', data)}
            />
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default InvoicesNew;
