import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Edit, Trash2, CreditCard, Banknote, Clock, Receipt } from 'lucide-react';
import { PaymentDetail, PaymentMethod } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { toast } from '@/hooks/use-toast';

interface PaymentDetailsProps {
  invoiceId: string;
  paymentDetails: PaymentDetail[];
  totalAmount: number;
  onAddPayment: (payment: Omit<PaymentDetail, 'id' | 'created_at'>) => Promise<void>;
  onUpdatePayment: (id: string, payment: Partial<PaymentDetail>) => Promise<void>;
  onDeletePayment: (id: string) => Promise<void>;
  allowEdit?: boolean;
}

const PaymentDetails: React.FC<PaymentDetailsProps> = ({
  invoiceId,
  paymentDetails,
  totalAmount,
  onAddPayment,
  onUpdatePayment,
  onDeletePayment,
  allowEdit = true
}) => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<PaymentDetail | null>(null);
  const [formData, setFormData] = useState({
    payment_method: 'cash' as PaymentMethod,
    amount: 0,
    reference_number: '',
    notes: ''
  });

  const totalPaid = paymentDetails.reduce((sum, payment) => sum + payment.amount, 0);
  const remainingAmount = totalAmount - totalPaid;

  const resetForm = () => {
    setFormData({
      payment_method: 'cash',
      amount: 0,
      reference_number: '',
      notes: ''
    });
  };

  const handleAddPayment = async () => {
    if (formData.amount <= 0) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال مبلغ صحيح",
        variant: "destructive"
      });
      return;
    }

    if (formData.amount > remainingAmount) {
      toast({
        title: "خطأ",
        description: "المبلغ المدخل أكبر من المبلغ المتبقي",
        variant: "destructive"
      });
      return;
    }

    try {
      await onAddPayment({
        invoice_id: invoiceId,
        payment_method: formData.payment_method,
        amount: formData.amount,
        reference_number: formData.reference_number || undefined,
        notes: formData.notes || undefined,
        created_by: undefined // سيتم تعيينه من السياق
      });

      setIsAddDialogOpen(false);
      resetForm();
      toast({
        title: "تم الإضافة",
        description: "تم إضافة الدفعة بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في إضافة الدفعة",
        variant: "destructive"
      });
    }
  };

  const handleEditPayment = async () => {
    if (!selectedPayment) return;

    if (formData.amount <= 0) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال مبلغ صحيح",
        variant: "destructive"
      });
      return;
    }

    try {
      await onUpdatePayment(selectedPayment.id, {
        payment_method: formData.payment_method,
        amount: formData.amount,
        reference_number: formData.reference_number || undefined,
        notes: formData.notes || undefined
      });

      setIsEditDialogOpen(false);
      setSelectedPayment(null);
      resetForm();
      toast({
        title: "تم التحديث",
        description: "تم تحديث الدفعة بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث الدفعة",
        variant: "destructive"
      });
    }
  };

  const handleDeletePayment = async (payment: PaymentDetail) => {
    if (!confirm(`هل أنت متأكد من حذف هذه الدفعة (${formatCurrency(payment.amount)})?`)) {
      return;
    }

    try {
      await onDeletePayment(payment.id);
      toast({
        title: "تم الحذف",
        description: "تم حذف الدفعة بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في حذف الدفعة",
        variant: "destructive"
      });
    }
  };

  const openEditDialog = (payment: PaymentDetail) => {
    setSelectedPayment(payment);
    setFormData({
      payment_method: payment.payment_method,
      amount: payment.amount,
      reference_number: payment.reference_number || '',
      notes: payment.notes || ''
    });
    setIsEditDialogOpen(true);
  };

  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case 'cash':
        return <Banknote className="w-4 h-4" />;
      case 'card':
        return <CreditCard className="w-4 h-4" />;
      case 'credit':
        return <Clock className="w-4 h-4" />;
      default:
        return <Receipt className="w-4 h-4" />;
    }
  };

  const getPaymentMethodLabel = (method: PaymentMethod) => {
    switch (method) {
      case 'cash':
        return 'نقداً';
      case 'card':
        return 'بطاقة';
      case 'credit':
        return 'آجل';
      case 'mixed':
        return 'مختلط';
      default:
        return method;
    }
  };

  const getPaymentStatusColor = () => {
    if (remainingAmount <= 0) return 'text-green-600';
    if (totalPaid > 0) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Card className="w-full" dir="rtl">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <Receipt className="w-5 h-5" />
            تفاصيل الدفع
          </span>
          {allowEdit && remainingAmount > 0 && (
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="w-4 h-4 ml-2" />
                  إضافة دفعة
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>إضافة دفعة جديدة</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label>طريقة الدفع</Label>
                    <Select
                      value={formData.payment_method}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, payment_method: value as PaymentMethod }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">نقداً</SelectItem>
                        <SelectItem value="card">بطاقة</SelectItem>
                        <SelectItem value="credit">آجل</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label>المبلغ (الحد الأقصى: {formatCurrency(remainingAmount)})</Label>
                    <Input
                      type="number"
                      min="0"
                      max={remainingAmount}
                      step="0.01"
                      value={formData.amount}
                      onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                    />
                  </div>
                  
                  <div>
                    <Label>رقم المرجع (اختياري)</Label>
                    <Input
                      placeholder="رقم الإيصال، رقم البطاقة، إلخ..."
                      value={formData.reference_number}
                      onChange={(e) => setFormData(prev => ({ ...prev, reference_number: e.target.value }))}
                    />
                  </div>
                  
                  <div>
                    <Label>ملاحظات (اختياري)</Label>
                    <Textarea
                      placeholder="أي ملاحظات إضافية..."
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      rows={3}
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={handleAddPayment} className="flex-1">
                      إضافة الدفعة
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsAddDialogOpen(false);
                        resetForm();
                      }}
                      className="flex-1"
                    >
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* ملخص الدفع */}
        <div className="grid grid-cols-3 gap-4">
          <div className="p-3 bg-blue-50 rounded-lg text-center">
            <div className="text-sm text-gray-600">إجمالي الفاتورة</div>
            <div className="text-lg font-bold text-blue-600">
              {formatCurrency(totalAmount)}
            </div>
          </div>
          
          <div className="p-3 bg-green-50 rounded-lg text-center">
            <div className="text-sm text-gray-600">المدفوع</div>
            <div className="text-lg font-bold text-green-600">
              {formatCurrency(totalPaid)}
            </div>
          </div>
          
          <div className="p-3 bg-gray-50 rounded-lg text-center">
            <div className="text-sm text-gray-600">المتبقي</div>
            <div className={`text-lg font-bold ${getPaymentStatusColor()}`}>
              {formatCurrency(remainingAmount)}
            </div>
          </div>
        </div>

        {/* حالة الدفع */}
        <div className="flex justify-center">
          <Badge variant={remainingAmount <= 0 ? 'default' : totalPaid > 0 ? 'secondary' : 'destructive'} className="text-sm">
            {remainingAmount <= 0 ? 'مدفوع بالكامل' : totalPaid > 0 ? 'مدفوع جزئياً' : 'غير مدفوع'}
          </Badge>
        </div>

        {/* قائمة الدفعات */}
        {paymentDetails.length > 0 ? (
          <div className="space-y-2">
            <h4 className="font-medium">سجل الدفعات:</h4>
            <div className="space-y-2">
              {paymentDetails.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getPaymentMethodIcon(payment.payment_method)}
                    <div>
                      <div className="font-medium">
                        {formatCurrency(payment.amount)} - {getPaymentMethodLabel(payment.payment_method)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(payment.created_at).toLocaleDateString('ar-SA')} - 
                        {new Date(payment.created_at).toLocaleTimeString('ar-SA')}
                      </div>
                      {payment.reference_number && (
                        <div className="text-sm text-gray-500">
                          مرجع: {payment.reference_number}
                        </div>
                      )}
                      {payment.notes && (
                        <div className="text-sm text-gray-500">
                          {payment.notes}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {allowEdit && (
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditDialog(payment)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeletePayment(payment)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-6 text-gray-500">
            لا توجد دفعات مسجلة
          </div>
        )}

        {/* نافذة تعديل الدفعة */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>تعديل الدفعة</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label>طريقة الدفع</Label>
                <Select
                  value={formData.payment_method}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, payment_method: value as PaymentMethod }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">نقداً</SelectItem>
                    <SelectItem value="card">بطاقة</SelectItem>
                    <SelectItem value="credit">آجل</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>المبلغ</Label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.amount}
                  onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                />
              </div>
              
              <div>
                <Label>رقم المرجع (اختياري)</Label>
                <Input
                  placeholder="رقم الإيصال، رقم البطاقة، إلخ..."
                  value={formData.reference_number}
                  onChange={(e) => setFormData(prev => ({ ...prev, reference_number: e.target.value }))}
                />
              </div>
              
              <div>
                <Label>ملاحظات (اختياري)</Label>
                <Textarea
                  placeholder="أي ملاحظات إضافية..."
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                />
              </div>
              
              <div className="flex gap-2">
                <Button onClick={handleEditPayment} className="flex-1">
                  حفظ التغييرات
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditDialogOpen(false);
                    setSelectedPayment(null);
                    resetForm();
                  }}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default PaymentDetails;
