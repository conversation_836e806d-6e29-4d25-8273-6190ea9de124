import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Filter, 
  Calendar, 
  User, 
  Package, 
  FileText, 
  Barcode,
  X,
  Clock,
  DollarSign
} from 'lucide-react';
import { Invoice, Customer, Supplier, Product, InvoiceType, PaymentStatus } from '@/types';
import { formatCurrency } from '@/utils/currency';

interface SearchFilters {
  searchTerm: string;
  invoiceType: InvoiceType | '';
  paymentStatus: PaymentStatus | '';
  customerId: string;
  supplierId: string;
  productId: string;
  dateFrom: string;
  dateTo: string;
  amountFrom: number | '';
  amountTo: number | '';
  createdBy: string;
}

interface SmartSearchProps {
  invoices: Invoice[];
  customers: Customer[];
  suppliers: Supplier[];
  products: Product[];
  onSearchResults: (results: Invoice[]) => void;
  onFilterChange?: (filters: SearchFilters) => void;
}

const SmartSearch: React.FC<SmartSearchProps> = ({
  invoices,
  customers,
  suppliers,
  products,
  onSearchResults,
  onFilterChange
}) => {
  const [filters, setFilters] = useState<SearchFilters>({
    searchTerm: '',
    invoiceType: '',
    paymentStatus: '',
    customerId: '',
    supplierId: '',
    productId: '',
    dateFrom: '',
    dateTo: '',
    amountFrom: '',
    amountTo: '',
    createdBy: ''
  });

  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    performSearch();
    onFilterChange?.(filters);
  }, [filters]);

  useEffect(() => {
    generateSearchSuggestions();
  }, [filters.searchTerm]);

  const performSearch = () => {
    let results = [...invoices];

    // البحث النصي
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      results = results.filter(invoice => 
        invoice.invoice_number.toLowerCase().includes(searchLower) ||
        invoice.customer?.name.toLowerCase().includes(searchLower) ||
        invoice.supplier?.name.toLowerCase().includes(searchLower) ||
        (invoice.notes && invoice.notes.toLowerCase().includes(searchLower)) ||
        invoice.items?.some(item => 
          item.product_name.toLowerCase().includes(searchLower) ||
          item.product?.barcode?.toLowerCase().includes(searchLower)
        )
      );
    }

    // تصفية حسب نوع الفاتورة
    if (filters.invoiceType) {
      results = results.filter(invoice => invoice.type === filters.invoiceType);
    }

    // تصفية حسب حالة الدفع
    if (filters.paymentStatus) {
      results = results.filter(invoice => invoice.payment_status === filters.paymentStatus);
    }

    // تصفية حسب العميل
    if (filters.customerId) {
      results = results.filter(invoice => invoice.customer_id === filters.customerId);
    }

    // تصفية حسب المورد
    if (filters.supplierId) {
      results = results.filter(invoice => invoice.supplier_id === filters.supplierId);
    }

    // تصفية حسب المنتج
    if (filters.productId) {
      results = results.filter(invoice => 
        invoice.items?.some(item => item.product_id === filters.productId)
      );
    }

    // تصفية حسب التاريخ
    if (filters.dateFrom) {
      results = results.filter(invoice => 
        new Date(invoice.invoice_date) >= new Date(filters.dateFrom)
      );
    }

    if (filters.dateTo) {
      results = results.filter(invoice => 
        new Date(invoice.invoice_date) <= new Date(filters.dateTo)
      );
    }

    // تصفية حسب المبلغ
    if (filters.amountFrom !== '') {
      results = results.filter(invoice => 
        invoice.total_amount >= Number(filters.amountFrom)
      );
    }

    if (filters.amountTo !== '') {
      results = results.filter(invoice => 
        invoice.total_amount <= Number(filters.amountTo)
      );
    }

    // تصفية حسب المنشئ
    if (filters.createdBy) {
      results = results.filter(invoice => invoice.created_by === filters.createdBy);
    }

    onSearchResults(results);
  };

  const generateSearchSuggestions = () => {
    if (!filters.searchTerm || filters.searchTerm.length < 2) {
      setSearchSuggestions([]);
      return;
    }

    const suggestions = new Set<string>();
    const searchLower = filters.searchTerm.toLowerCase();

    // اقتراحات من أرقام الفواتير
    invoices.forEach(invoice => {
      if (invoice.invoice_number.toLowerCase().includes(searchLower)) {
        suggestions.add(invoice.invoice_number);
      }
    });

    // اقتراحات من أسماء العملاء
    customers.forEach(customer => {
      if (customer.name.toLowerCase().includes(searchLower)) {
        suggestions.add(customer.name);
      }
    });

    // اقتراحات من أسماء الموردين
    suppliers.forEach(supplier => {
      if (supplier.name.toLowerCase().includes(searchLower)) {
        suggestions.add(supplier.name);
      }
    });

    // اقتراحات من أسماء المنتجات
    products.forEach(product => {
      if (product.name.toLowerCase().includes(searchLower)) {
        suggestions.add(product.name);
      }
      if (product.barcode && product.barcode.toLowerCase().includes(searchLower)) {
        suggestions.add(product.barcode);
      }
    });

    setSearchSuggestions(Array.from(suggestions).slice(0, 8));
  };

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      searchTerm: '',
      invoiceType: '',
      paymentStatus: '',
      customerId: '',
      supplierId: '',
      productId: '',
      dateFrom: '',
      dateTo: '',
      amountFrom: '',
      amountTo: '',
      createdBy: ''
    });
    setIsAdvancedOpen(false);
  };

  const getActiveFiltersCount = () => {
    return Object.entries(filters).filter(([key, value]) => 
      key !== 'searchTerm' && value !== '' && value !== null
    ).length;
  };

  const applySuggestion = (suggestion: string) => {
    updateFilter('searchTerm', suggestion);
    setShowSuggestions(false);
  };

  return (
    <Card className="w-full" dir="rtl">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            البحث الذكي
          </span>
          <div className="flex items-center gap-2">
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary">
                {getActiveFiltersCount()} مرشح نشط
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
            >
              <Filter className="w-4 h-4 ml-2" />
              بحث متقدم
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* البحث الأساسي */}
        <div className="relative">
          <div className="relative">
            <Search className="absolute right-3 top-3 w-4 h-4 text-gray-400" />
            <Input
              ref={searchInputRef}
              placeholder="ابحث برقم الفاتورة، العميل، المورد، المنتج، أو الباركود..."
              value={filters.searchTerm}
              onChange={(e) => updateFilter('searchTerm', e.target.value)}
              onFocus={() => setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              className="pr-10"
            />
          </div>
          
          {/* اقتراحات البحث */}
          {showSuggestions && searchSuggestions.length > 0 && (
            <div className="absolute top-full left-0 right-0 z-10 mt-1 bg-white border rounded-lg shadow-lg max-h-48 overflow-y-auto">
              {searchSuggestions.map((suggestion, index) => (
                <button
                  key={index}
                  className="w-full text-right px-4 py-2 hover:bg-gray-50 border-b last:border-b-0"
                  onClick={() => applySuggestion(suggestion)}
                >
                  {suggestion}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* البحث المتقدم */}
        {isAdvancedOpen && (
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* نوع الفاتورة */}
              <div>
                <Label className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  نوع الفاتورة
                </Label>
                <Select
                  value={filters.invoiceType}
                  onValueChange={(value) => updateFilter('invoiceType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الأنواع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الأنواع</SelectItem>
                    <SelectItem value="sales">بيع</SelectItem>
                    <SelectItem value="purchase">شراء</SelectItem>
                    <SelectItem value="sales_return">إرجاع بيع</SelectItem>
                    <SelectItem value="purchase_return">إرجاع شراء</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* حالة الدفع */}
              <div>
                <Label className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  حالة الدفع
                </Label>
                <Select
                  value={filters.paymentStatus}
                  onValueChange={(value) => updateFilter('paymentStatus', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الحالات" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الحالات</SelectItem>
                    <SelectItem value="paid">مدفوع</SelectItem>
                    <SelectItem value="partial">جزئي</SelectItem>
                    <SelectItem value="unpaid">غير مدفوع</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* العميل */}
              <div>
                <Label className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  العميل
                </Label>
                <Select
                  value={filters.customerId}
                  onValueChange={(value) => updateFilter('customerId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="جميع العملاء" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع العملاء</SelectItem>
                    {customers.map(customer => (
                      <SelectItem key={customer.id} value={customer.id}>
                        {customer.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* المورد */}
              <div>
                <Label className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  المورد
                </Label>
                <Select
                  value={filters.supplierId}
                  onValueChange={(value) => updateFilter('supplierId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الموردين" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الموردين</SelectItem>
                    {suppliers.map(supplier => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* المنتج */}
              <div>
                <Label className="flex items-center gap-2">
                  <Package className="w-4 h-4" />
                  المنتج
                </Label>
                <Select
                  value={filters.productId}
                  onValueChange={(value) => updateFilter('productId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="جميع المنتجات" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع المنتجات</SelectItem>
                    {products.map(product => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* من تاريخ */}
              <div>
                <Label className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  من تاريخ
                </Label>
                <Input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => updateFilter('dateFrom', e.target.value)}
                />
              </div>

              {/* إلى تاريخ */}
              <div>
                <Label className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  إلى تاريخ
                </Label>
                <Input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => updateFilter('dateTo', e.target.value)}
                />
              </div>

              {/* من مبلغ */}
              <div>
                <Label>من مبلغ</Label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  value={filters.amountFrom}
                  onChange={(e) => updateFilter('amountFrom', e.target.value ? parseFloat(e.target.value) : '')}
                />
              </div>

              {/* إلى مبلغ */}
              <div>
                <Label>إلى مبلغ</Label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  value={filters.amountTo}
                  onChange={(e) => updateFilter('amountTo', e.target.value ? parseFloat(e.target.value) : '')}
                />
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={clearFilters}>
                <X className="w-4 h-4 ml-2" />
                مسح الكل
              </Button>
            </div>
          </div>
        )}

        {/* عرض المرشحات النشطة */}
        {getActiveFiltersCount() > 0 && (
          <div className="flex flex-wrap gap-2">
            {Object.entries(filters).map(([key, value]) => {
              if (key === 'searchTerm' || !value) return null;
              
              let label = '';
              let displayValue = value;

              switch (key) {
                case 'invoiceType':
                  label = 'النوع';
                  displayValue = value === 'sales' ? 'بيع' : 
                               value === 'purchase' ? 'شراء' :
                               value === 'sales_return' ? 'إرجاع بيع' : 'إرجاع شراء';
                  break;
                case 'paymentStatus':
                  label = 'الحالة';
                  displayValue = value === 'paid' ? 'مدفوع' :
                               value === 'partial' ? 'جزئي' : 'غير مدفوع';
                  break;
                case 'customerId':
                  label = 'العميل';
                  displayValue = customers.find(c => c.id === value)?.name || value;
                  break;
                case 'supplierId':
                  label = 'المورد';
                  displayValue = suppliers.find(s => s.id === value)?.name || value;
                  break;
                case 'productId':
                  label = 'المنتج';
                  displayValue = products.find(p => p.id === value)?.name || value;
                  break;
                case 'dateFrom':
                  label = 'من';
                  displayValue = new Date(value as string).toLocaleDateString('ar-SA');
                  break;
                case 'dateTo':
                  label = 'إلى';
                  displayValue = new Date(value as string).toLocaleDateString('ar-SA');
                  break;
                case 'amountFrom':
                  label = 'من مبلغ';
                  displayValue = formatCurrency(Number(value));
                  break;
                case 'amountTo':
                  label = 'إلى مبلغ';
                  displayValue = formatCurrency(Number(value));
                  break;
                default:
                  return null;
              }

              return (
                <Badge key={key} variant="secondary" className="flex items-center gap-1">
                  <span>{label}: {displayValue}</span>
                  <button
                    onClick={() => updateFilter(key as keyof SearchFilters, '')}
                    className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SmartSearch;
