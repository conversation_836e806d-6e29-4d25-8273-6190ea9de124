import React from 'react';
import { Layout } from "@/components/Layout";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import EnhancedDashboard from "@/components/dashboard/EnhancedDashboard";
import { Invoice, Customer, Supplier, Product, User } from "@/types";

const DashboardNew = () => {
  // جلب البيانات من قاعدة البيانات
  const { data: invoices = [], isLoading: invoicesLoading } = useQuery({
    queryKey: ['dashboard-invoices'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('invoices')
        .select(`
          *,
          customers (id, name, phone, address),
          suppliers (id, name, phone, address),
          invoice_items (
            id,
            product_id,
            product_name,
            quantity,
            unit_price,
            discount_amount,
            discount_percent,
            tax_amount,
            tax_percent,
            total_price,
            notes
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Invoice[];
    }
  });

  const { data: customers = [], isLoading: customersLoading } = useQuery({
    queryKey: ['dashboard-customers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .order('name');
      if (error) throw error;
      return data.map(customer => ({
        ...customer,
        balance: 0,
        totalPurchases: 0,
        createdAt: new Date(customer.created_at)
      })) as Customer[];
    }
  });

  const { data: suppliers = [], isLoading: suppliersLoading } = useQuery({
    queryKey: ['dashboard-suppliers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('suppliers')
        .select('*')
        .order('name');
      if (error) throw error;
      return data.map(supplier => ({
        ...supplier,
        balance: 0,
        totalPurchases: 0,
        createdAt: new Date(supplier.created_at)
      })) as Supplier[];
    }
  });

  const { data: products = [], isLoading: productsLoading } = useQuery({
    queryKey: ['dashboard-products'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .order('name');
      if (error) throw error;
      return data.map(product => ({
        id: product.id,
        name: product.name,
        nameAr: product.name,
        category: product.category || 'عام',
        brand: product.brand || '',
        barcode: product.barcode || '',
        unit: product.unit || 'قطعة',
        purchasePrice: product.purchase_price || 0,
        salePrice: product.selling_price || 0,
        stock: product.stock_quantity || 0,
        minStock: product.min_stock_level || 0,
        description: product.description,
        image: product.image_url,
        createdAt: new Date(product.created_at),
        updatedAt: new Date(product.updated_at)
      })) as Product[];
    }
  });

  // مستخدم وهمي للاختبار - يجب استبداله بالمستخدم الحقيقي
  const currentUser: User = {
    id: 'current-user',
    username: 'admin',
    full_name: 'المدير العام',
    email: '<EMAIL>',
    role_id: 'admin-role',
    role: {
      id: 'admin-role',
      name: 'admin',
      name_ar: 'مدير عام',
      description: 'صلاحيات كاملة',
      permissions: {
        invoices: {
          create: true,
          read: true,
          update: true,
          delete: true,
          print: true,
          return: true,
          suspend: true
        },
        products: {
          create: true,
          read: true,
          update: true,
          delete: true,
          manage_stock: true
        },
        customers: {
          create: true,
          read: true,
          update: true,
          delete: true,
          view_balance: true
        },
        suppliers: {
          create: true,
          read: true,
          update: true,
          delete: true,
          view_balance: true
        },
        reports: {
          view: true,
          export: true,
          financial: true,
          inventory: true
        },
        settings: {
          view: true,
          update: true,
          backup: true,
          restore: true
        },
        users: {
          create: true,
          read: true,
          update: true,
          delete: true,
          manage_roles: true
        }
      },
      is_active: true,
      created_at: new Date().toISOString()
    },
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const isLoading = invoicesLoading || customersLoading || suppliersLoading || productsLoading;

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">جاري تحميل لوحة التحكم...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <EnhancedDashboard
        invoices={invoices}
        customers={customers}
        suppliers={suppliers}
        products={products}
        currentUser={currentUser}
      />
    </Layout>
  );
};

export default DashboardNew;
