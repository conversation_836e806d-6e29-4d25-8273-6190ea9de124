
export interface Product {
  id: string;
  name: string;
  nameAr: string;
  category: string;
  brand: string;
  barcode: string;
  unit: string;
  purchasePrice: number;
  salePrice: number;
  stock: number;
  minStock: number;
  description?: string;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  city: string;
  address?: string;
  balance: number;
  totalPurchases: number;
  createdAt: Date;
}

export interface Supplier {
  id: string;
  name: string;
  phone: string;
  address: string;
  balance: number;
  totalPurchases: number;
  createdAt: Date;
}

// أنواع الفواتير المحسنة
export type InvoiceType = 'sales' | 'purchase' | 'sales_return' | 'purchase_return';
export type PaymentMethod = 'cash' | 'card' | 'credit' | 'mixed';
export type PaymentStatus = 'paid' | 'partial' | 'unpaid';

export interface Invoice {
  id: string;
  invoice_number: string;
  type: InvoiceType;
  customer_id?: string;
  supplier_id?: string;
  reference_invoice_id?: string; // للإرجاع
  return_reason?: string;
  items: InvoiceItem[];
  subtotal_amount: number;
  discount_amount: number;
  discount_percent: number;
  tax_amount: number;
  tax_rate: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  cash_amount: number;
  card_amount: number;
  credit_amount: number;
  payment_method: PaymentMethod;
  payment_status: PaymentStatus;
  currency: string;
  exchange_rate: number;
  invoice_date: string;
  due_date?: string;
  notes?: string;
  is_suspended: boolean;
  suspended_at?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;

  // العلاقات
  customer?: Customer;
  supplier?: Supplier;
  reference_invoice?: Invoice;
  payment_details?: PaymentDetail[];
}

export interface InvoiceItem {
  id?: string;
  invoice_id?: string;
  product_id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  original_price?: number;
  discount_amount: number;
  discount_percent: number;
  tax_amount: number;
  tax_percent: number;
  total_price: number;
  notes?: string;

  // العلاقات
  product?: Product;
}

// تفاصيل الدفع
export interface PaymentDetail {
  id: string;
  invoice_id: string;
  payment_method: PaymentMethod;
  amount: number;
  reference_number?: string;
  notes?: string;
  created_at: string;
  created_by?: string;
}

// الفواتير المعلقة
export interface SuspendedInvoice {
  id: string;
  invoice_data: any;
  customer_id?: string;
  supplier_id?: string;
  suspended_by: string;
  suspended_at: string;
  notes?: string;
  is_active: boolean;
}

// الأرقام التسلسلية
export interface InvoiceSequence {
  id: string;
  invoice_type: InvoiceType;
  year: number;
  last_number: number;
  prefix: string;
  suffix: string;
  created_at: string;
}

// الأدوار والصلاحيات
export interface UserRole {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  permissions: Record<string, any>;
  is_active: boolean;
  created_at: string;
}

export interface User {
  id: string;
  username: string;
  full_name: string;
  email?: string;
  phone?: string;
  role_id?: string;
  role?: UserRole;
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

// سجل العمليات
export interface AuditLog {
  id: string;
  table_name: string;
  record_id: string;
  action: 'INSERT' | 'UPDATE' | 'DELETE';
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  user_id?: string;
  created_at: string;
}
