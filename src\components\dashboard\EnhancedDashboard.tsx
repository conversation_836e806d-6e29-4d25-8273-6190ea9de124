import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  ShoppingCart, 
  Users, 
  Package,
  AlertTriangle,
  Calendar,
  Clock,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Star,
  ArrowRight
} from 'lucide-react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  BarChart,
  Bar
} from 'recharts';
import { Invoice, Customer, Supplier, Product, User } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { usePermissions } from '@/components/permissions/PermissionGuard';

interface EnhancedDashboardProps {
  invoices: Invoice[];
  customers: Customer[];
  suppliers: Supplier[];
  products: Product[];
  currentUser?: User;
}

const EnhancedDashboard: React.FC<EnhancedDashboardProps> = ({
  invoices,
  customers,
  suppliers,
  products,
  currentUser
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('today');
  const permissions = usePermissions(currentUser);

  // حساب البيانات للفترة المحددة
  const dashboardData = useMemo(() => {
    const now = new Date();
    let startDate: Date;

    switch (selectedPeriod) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    }

    const filteredInvoices = invoices.filter(invoice => 
      new Date(invoice.invoice_date) >= startDate
    );

    // الإحصائيات الأساسية
    const salesInvoices = filteredInvoices.filter(i => i.type === 'sales');
    const purchaseInvoices = filteredInvoices.filter(i => i.type === 'purchase');
    const returnInvoices = filteredInvoices.filter(i => i.type.includes('return'));

    const totalSales = salesInvoices.reduce((sum, i) => sum + i.total_amount, 0);
    const totalPurchases = purchaseInvoices.reduce((sum, i) => sum + i.total_amount, 0);
    const totalReturns = returnInvoices.reduce((sum, i) => sum + i.total_amount, 0);
    const netSales = totalSales - totalReturns;
    const grossProfit = netSales - totalPurchases;

    const paidAmount = filteredInvoices.reduce((sum, i) => sum + i.paid_amount, 0);
    const pendingAmount = filteredInvoices.reduce((sum, i) => sum + i.remaining_amount, 0);

    // المنتجات منخفضة المخزون
    const lowStockProducts = products.filter(p => p.stock <= p.minStock);
    const outOfStockProducts = products.filter(p => p.stock <= 0);

    // أفضل العملاء
    const customerSales = new Map<string, number>();
    salesInvoices.forEach(invoice => {
      if (invoice.customer_id) {
        const current = customerSales.get(invoice.customer_id) || 0;
        customerSales.set(invoice.customer_id, current + invoice.total_amount);
      }
    });

    const topCustomers = Array.from(customerSales.entries())
      .map(([customerId, total]) => ({
        customer: customers.find(c => c.id === customerId),
        total
      }))
      .filter(item => item.customer)
      .sort((a, b) => b.total - a.total)
      .slice(0, 5);

    // أفضل المنتجات
    const productSales = new Map<string, { quantity: number; revenue: number }>();
    salesInvoices.forEach(invoice => {
      invoice.items?.forEach(item => {
        const current = productSales.get(item.product_id) || { quantity: 0, revenue: 0 };
        current.quantity += item.quantity;
        current.revenue += item.total_price;
        productSales.set(item.product_id, current);
      });
    });

    const topProducts = Array.from(productSales.entries())
      .map(([productId, data]) => ({
        product: products.find(p => p.id === productId),
        ...data
      }))
      .filter(item => item.product)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // بيانات الرسم البياني للمبيعات اليومية
    const dailySales = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      
      const dayInvoices = invoices.filter(invoice => 
        invoice.invoice_date === dateStr && invoice.type === 'sales'
      );
      
      const dayTotal = dayInvoices.reduce((sum, i) => sum + i.total_amount, 0);
      
      dailySales.push({
        date: date.toLocaleDateString('ar-SA', { weekday: 'short' }),
        sales: dayTotal,
        invoices: dayInvoices.length
      });
    }

    // توزيع حالة الدفع
    const paymentStatus = [
      { 
        name: 'مدفوع', 
        value: filteredInvoices.filter(i => i.payment_status === 'paid').length,
        color: '#10B981'
      },
      { 
        name: 'جزئي', 
        value: filteredInvoices.filter(i => i.payment_status === 'partial').length,
        color: '#F59E0B'
      },
      { 
        name: 'غير مدفوع', 
        value: filteredInvoices.filter(i => i.payment_status === 'unpaid').length,
        color: '#EF4444'
      }
    ];

    return {
      totalSales,
      totalPurchases,
      totalReturns,
      netSales,
      grossProfit,
      paidAmount,
      pendingAmount,
      invoicesCount: filteredInvoices.length,
      lowStockProducts,
      outOfStockProducts,
      topCustomers,
      topProducts,
      dailySales,
      paymentStatus
    };
  }, [invoices, customers, suppliers, products, selectedPeriod]);

  const COLORS = ['#10B981', '#F59E0B', '#EF4444', '#3B82F6', '#8B5CF6'];

  return (
    <div className="space-y-6" dir="rtl">
      {/* رأس لوحة التحكم */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">لوحة التحكم</h1>
          <p className="text-gray-600">
            مرحباً {currentUser?.full_name}، إليك ملخص أعمالك اليوم
          </p>
        </div>
        
        <div className="flex gap-2">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="p-2 border rounded-lg"
          >
            <option value="today">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="year">هذا العام</option>
          </select>
        </div>
      </div>

      {/* البطاقات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {permissions.canViewReports() && (
          <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">صافي المبيعات</p>
                  <p className="text-3xl font-bold">
                    {formatCurrency(dashboardData.netSales)}
                  </p>
                  <p className="text-sm text-green-100">
                    {dashboardData.invoicesCount} فاتورة
                  </p>
                </div>
                <TrendingUp className="w-12 h-12 text-green-200" />
              </div>
            </CardContent>
          </Card>
        )}

        <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100">الربح الإجمالي</p>
                <p className="text-3xl font-bold">
                  {formatCurrency(dashboardData.grossProfit)}
                </p>
                <p className="text-sm text-blue-100">
                  هامش {dashboardData.netSales > 0 ? ((dashboardData.grossProfit / dashboardData.netSales) * 100).toFixed(1) : 0}%
                </p>
              </div>
              <Target className="w-12 h-12 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100">المبالغ المعلقة</p>
                <p className="text-3xl font-bold">
                  {formatCurrency(dashboardData.pendingAmount)}
                </p>
                <p className="text-sm text-orange-100">
                  {((dashboardData.pendingAmount / Math.max(dashboardData.totalSales, 1)) * 100).toFixed(1)}% من المبيعات
                </p>
              </div>
              <Clock className="w-12 h-12 text-orange-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-500 to-red-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-100">تنبيهات المخزون</p>
                <p className="text-3xl font-bold">
                  {dashboardData.lowStockProducts.length}
                </p>
                <p className="text-sm text-red-100">
                  {dashboardData.outOfStockProducts.length} نفد تماماً
                </p>
              </div>
              <AlertTriangle className="w-12 h-12 text-red-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* الرسم البياني للمبيعات */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              اتجاه المبيعات (آخر 7 أيام)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={dashboardData.dailySales}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip 
                  formatter={(value) => [formatCurrency(Number(value)), 'المبيعات']}
                  labelFormatter={(label) => `يوم ${label}`}
                />
                <Area 
                  type="monotone" 
                  dataKey="sales" 
                  stroke="#10B981" 
                  fill="#10B981" 
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* توزيع حالة الدفع */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5" />
              حالة الدفع
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={dashboardData.paymentStatus}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {dashboardData.paymentStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* التبويبات */}
      <Tabs defaultValue="customers">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="customers">أفضل العملاء</TabsTrigger>
          <TabsTrigger value="products">أفضل المنتجات</TabsTrigger>
          <TabsTrigger value="alerts">التنبيهات</TabsTrigger>
        </TabsList>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                أفضل العملاء
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardData.topCustomers.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div>
                        <div className="font-medium">{item.customer?.name}</div>
                        <div className="text-sm text-gray-500">{item.customer?.phone}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-green-600">
                        {formatCurrency(item.total)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {selectedPeriod === 'today' ? 'اليوم' : 'في الفترة'}
                      </div>
                    </div>
                  </div>
                ))}
                
                {dashboardData.topCustomers.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد مبيعات في هذه الفترة
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                أفضل المنتجات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardData.topProducts.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div>
                        <div className="font-medium">{item.product?.name}</div>
                        <div className="text-sm text-gray-500">
                          {item.quantity} قطعة مباعة
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-blue-600">
                        {formatCurrency(item.revenue)}
                      </div>
                      <div className="text-sm text-gray-500">
                        إيرادات
                      </div>
                    </div>
                  </div>
                ))}
                
                {dashboardData.topProducts.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد مبيعات في هذه الفترة
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                تنبيهات المخزون
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardData.outOfStockProducts.map((product) => (
                  <div key={product.id} className="flex items-center justify-between p-4 border-l-4 border-l-red-500 bg-red-50 rounded-lg">
                    <div className="flex items-center gap-4">
                      <AlertTriangle className="w-5 h-5 text-red-500" />
                      <div>
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-red-600">نفد من المخزون</div>
                      </div>
                    </div>
                    <Badge variant="destructive">نفد</Badge>
                  </div>
                ))}

                {dashboardData.lowStockProducts.filter(p => p.stock > 0).map((product) => (
                  <div key={product.id} className="flex items-center justify-between p-4 border-l-4 border-l-yellow-500 bg-yellow-50 rounded-lg">
                    <div className="flex items-center gap-4">
                      <AlertTriangle className="w-5 h-5 text-yellow-500" />
                      <div>
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-yellow-600">
                          مخزون منخفض: {product.stock} متبقي
                        </div>
                      </div>
                    </div>
                    <Badge variant="secondary">{product.stock}</Badge>
                  </div>
                ))}
                
                {dashboardData.lowStockProducts.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    جميع المنتجات متوفرة بكميات كافية
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* إجراءات سريعة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            إجراءات سريعة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {permissions.canCreateInvoices() && (
              <Button className="h-20 flex-col gap-2">
                <ShoppingCart className="w-6 h-6" />
                <span>فاتورة بيع جديدة</span>
              </Button>
            )}
            
            {permissions.canManageProducts() && (
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Package className="w-6 h-6" />
                <span>إضافة منتج</span>
              </Button>
            )}
            
            {permissions.canManageCustomers() && (
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Users className="w-6 h-6" />
                <span>عميل جديد</span>
              </Button>
            )}
            
            {permissions.canViewReports() && (
              <Button variant="outline" className="h-20 flex-col gap-2">
                <BarChart3 className="w-6 h-6" />
                <span>التقارير</span>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedDashboard;
