import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { RotateCcw, Save, Printer, Search } from 'lucide-react';
import { Invoice, InvoiceItem, Customer, Supplier, InvoiceType } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { toast } from '@/hooks/use-toast';

interface ReturnInvoiceProps {
  returnType: 'sales_return' | 'purchase_return';
  invoice?: Invoice;
  onSave: (invoice: Partial<Invoice>) => Promise<void>;
  onSearchOriginalInvoice?: (invoiceNumber: string) => Promise<Invoice | null>;
  customers?: Customer[];
  suppliers?: Supplier[];
}

const ReturnInvoice: React.FC<ReturnInvoiceProps> = ({
  returnType,
  invoice,
  onSave,
  onSearchOriginalInvoice,
  customers = [],
  suppliers = []
}) => {
  const [formData, setFormData] = useState<Partial<Invoice>>({
    type: returnType,
    reference_invoice_id: '',
    customer_id: returnType === 'sales_return' ? '' : undefined,
    supplier_id: returnType === 'purchase_return' ? '' : undefined,
    return_reason: '',
    items: [],
    subtotal_amount: 0,
    discount_amount: 0,
    tax_amount: 0,
    total_amount: 0,
    paid_amount: 0,
    remaining_amount: 0,
    payment_method: 'cash',
    payment_status: 'unpaid',
    currency: 'DZD',
    exchange_rate: 1.0,
    invoice_date: new Date().toISOString().split('T')[0],
    notes: '',
    is_suspended: false
  });

  const [originalInvoice, setOriginalInvoice] = useState<Invoice | null>(null);
  const [searchInvoiceNumber, setSearchInvoiceNumber] = useState('');
  const [selectedItems, setSelectedItems] = useState<{ [key: number]: { selected: boolean; quantity: number } }>({});

  useEffect(() => {
    if (invoice) {
      setFormData(invoice);
    }
  }, [invoice]);

  useEffect(() => {
    calculateTotals();
  }, [formData.items]);

  const calculateTotals = () => {
    const items = formData.items || [];
    const subtotal = items.reduce((sum, item) => sum + (item.total_price || 0), 0);
    const discount = items.reduce((sum, item) => sum + (item.discount_amount || 0), 0);
    const tax = items.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
    const total = subtotal;

    setFormData(prev => ({
      ...prev,
      subtotal_amount: subtotal,
      discount_amount: discount,
      tax_amount: tax,
      total_amount: total,
      remaining_amount: total - (prev.paid_amount || 0)
    }));
  };

  const searchOriginalInvoice = async () => {
    if (!searchInvoiceNumber.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال رقم الفاتورة الأصلية",
        variant: "destructive"
      });
      return;
    }

    if (!onSearchOriginalInvoice) {
      toast({
        title: "خطأ",
        description: "وظيفة البحث غير متاحة",
        variant: "destructive"
      });
      return;
    }

    try {
      const foundInvoice = await onSearchOriginalInvoice(searchInvoiceNumber);
      if (foundInvoice) {
        // التحقق من نوع الفاتورة
        const expectedType = returnType === 'sales_return' ? 'sales' : 'purchase';
        if (foundInvoice.type !== expectedType) {
          toast({
            title: "خطأ",
            description: `نوع الفاتورة غير صحيح. يجب أن تكون فاتورة ${expectedType === 'sales' ? 'بيع' : 'شراء'}`,
            variant: "destructive"
          });
          return;
        }

        setOriginalInvoice(foundInvoice);
        setFormData(prev => ({
          ...prev,
          reference_invoice_id: foundInvoice.id,
          customer_id: foundInvoice.customer_id,
          supplier_id: foundInvoice.supplier_id
        }));

        // تهيئة العناصر المحددة
        const initialSelection: { [key: number]: { selected: boolean; quantity: number } } = {};
        foundInvoice.items?.forEach((_, index) => {
          initialSelection[index] = { selected: false, quantity: 0 };
        });
        setSelectedItems(initialSelection);

        toast({
          title: "تم العثور على الفاتورة",
          description: `تم تحميل فاتورة رقم ${foundInvoice.invoice_number}`
        });
      } else {
        toast({
          title: "لم يتم العثور على الفاتورة",
          description: "لا توجد فاتورة بهذا الرقم",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في البحث عن الفاتورة",
        variant: "destructive"
      });
    }
  };

  const toggleItemSelection = (index: number, selected: boolean) => {
    setSelectedItems(prev => ({
      ...prev,
      [index]: {
        ...prev[index],
        selected,
        quantity: selected ? (originalInvoice?.items?.[index]?.quantity || 0) : 0
      }
    }));
  };

  const updateReturnQuantity = (index: number, quantity: number) => {
    const originalQuantity = originalInvoice?.items?.[index]?.quantity || 0;
    if (quantity > originalQuantity) {
      toast({
        title: "خطأ",
        description: `الكمية المرجعة لا يمكن أن تتجاوز الكمية الأصلية (${originalQuantity})`,
        variant: "destructive"
      });
      return;
    }

    setSelectedItems(prev => ({
      ...prev,
      [index]: {
        ...prev[index],
        quantity
      }
    }));
  };

  const addSelectedItemsToReturn = () => {
    if (!originalInvoice) return;

    const returnItems: InvoiceItem[] = [];
    
    Object.entries(selectedItems).forEach(([indexStr, selection]) => {
      const index = parseInt(indexStr);
      if (selection.selected && selection.quantity > 0) {
        const originalItem = originalInvoice.items?.[index];
        if (originalItem) {
          const returnItem: InvoiceItem = {
            ...originalItem,
            quantity: selection.quantity,
            total_price: originalItem.unit_price * selection.quantity,
            discount_amount: (originalItem.discount_amount || 0) * (selection.quantity / originalItem.quantity),
            tax_amount: (originalItem.tax_amount || 0) * (selection.quantity / originalItem.quantity)
          };
          returnItems.push(returnItem);
        }
      }
    });

    setFormData(prev => ({
      ...prev,
      items: returnItems
    }));

    toast({
      title: "تم إضافة العناصر",
      description: `تم إضافة ${returnItems.length} عنصر للإرجاع`
    });
  };

  const handleSave = async () => {
    if (!formData.return_reason?.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال سبب الإرجاع",
        variant: "destructive"
      });
      return;
    }

    if (!formData.items || formData.items.length === 0) {
      toast({
        title: "خطأ",
        description: "يرجى إضافة عنصر واحد على الأقل للإرجاع",
        variant: "destructive"
      });
      return;
    }

    try {
      await onSave(formData);
      toast({
        title: "تم الحفظ",
        description: "تم حفظ فاتورة الإرجاع بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في حفظ فاتورة الإرجاع",
        variant: "destructive"
      });
    }
  };

  const isSearchEnabled = !!onSearchOriginalInvoice;
  const title = returnType === 'sales_return' ? 'فاتورة إرجاع بيع' : 'فاتورة إرجاع شراء';
  const entityLabel = returnType === 'sales_return' ? 'العميل' : 'المورد';
  const entityList = returnType === 'sales_return' ? customers : suppliers;

  return (
    <div className="space-y-6 p-6" dir="rtl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <RotateCcw className="w-5 h-5 ml-2" />
              {title}
            </span>
            <Badge variant="outline">
              {formData.invoice_number || 'جديدة'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* البحث عن الفاتورة الأصلية */}
          {isSearchEnabled && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">البحث عن الفاتورة الأصلية</h3>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Label>رقم الفاتورة الأصلية</Label>
                  <Input
                    placeholder="أدخل رقم الفاتورة..."
                    value={searchInvoiceNumber}
                    onChange={(e) => setSearchInvoiceNumber(e.target.value)}
                  />
                </div>
                <div className="flex items-end">
                  <Button onClick={searchOriginalInvoice}>
                    <Search className="w-4 h-4 ml-2" />
                    بحث
                  </Button>
                </div>
              </div>
              
              {originalInvoice && (
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <h4 className="font-semibold text-green-800">تم العثور على الفاتورة:</h4>
                  <div className="mt-2 text-sm text-green-700">
                    <p>رقم الفاتورة: {originalInvoice.invoice_number}</p>
                    <p>التاريخ: {new Date(originalInvoice.invoice_date).toLocaleDateString('ar-SA')}</p>
                    <p>المجموع: {formatCurrency(originalInvoice.total_amount)}</p>
                    {returnType === 'sales_return' && originalInvoice.customer && (
                      <p>العميل: {originalInvoice.customer.name}</p>
                    )}
                    {returnType === 'purchase_return' && originalInvoice.supplier && (
                      <p>المورد: {originalInvoice.supplier.name}</p>
                    )}
                  </div>
                </div>
              )}
              
              <Separator />
            </div>
          )}

          {/* معلومات الإرجاع */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label>{entityLabel}</Label>
              <Select
                value={returnType === 'sales_return' ? formData.customer_id : formData.supplier_id}
                onValueChange={(value) => {
                  if (returnType === 'sales_return') {
                    setFormData(prev => ({ ...prev, customer_id: value }));
                  } else {
                    setFormData(prev => ({ ...prev, supplier_id: value }));
                  }
                }}
                disabled={!!originalInvoice}
              >
                <SelectTrigger>
                  <SelectValue placeholder={`اختر ${entityLabel}`} />
                </SelectTrigger>
                <SelectContent>
                  {entityList.map(entity => (
                    <SelectItem key={entity.id} value={entity.id}>
                      {entity.name} - {entity.phone}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>تاريخ الإرجاع</Label>
              <Input
                type="date"
                value={formData.invoice_date}
                onChange={(e) => setFormData(prev => ({ ...prev, invoice_date: e.target.value }))}
              />
            </div>
            <div>
              <Label>سبب الإرجاع *</Label>
              <Input
                placeholder="أدخل سبب الإرجاع..."
                value={formData.return_reason}
                onChange={(e) => setFormData(prev => ({ ...prev, return_reason: e.target.value }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* اختيار العناصر للإرجاع */}
      {originalInvoice && originalInvoice.items && (
        <Card>
          <CardHeader>
            <CardTitle>اختيار العناصر للإرجاع</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-2">اختيار</th>
                      <th className="text-right p-2">المنتج</th>
                      <th className="text-right p-2">الكمية الأصلية</th>
                      <th className="text-right p-2">كمية الإرجاع</th>
                      <th className="text-right p-2">السعر</th>
                      <th className="text-right p-2">المجموع</th>
                    </tr>
                  </thead>
                  <tbody>
                    {originalInvoice.items.map((item, index) => (
                      <tr key={index} className="border-b">
                        <td className="p-2">
                          <input
                            type="checkbox"
                            checked={selectedItems[index]?.selected || false}
                            onChange={(e) => toggleItemSelection(index, e.target.checked)}
                            className="w-4 h-4"
                          />
                        </td>
                        <td className="p-2">{item.product_name}</td>
                        <td className="p-2">{item.quantity}</td>
                        <td className="p-2">
                          <Input
                            type="number"
                            min="0"
                            max={item.quantity}
                            value={selectedItems[index]?.quantity || 0}
                            onChange={(e) => updateReturnQuantity(index, parseInt(e.target.value) || 0)}
                            disabled={!selectedItems[index]?.selected}
                            className="w-20"
                          />
                        </td>
                        <td className="p-2">{formatCurrency(item.unit_price)}</td>
                        <td className="p-2">
                          {formatCurrency((selectedItems[index]?.quantity || 0) * item.unit_price)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="flex justify-end">
                <Button onClick={addSelectedItemsToReturn}>
                  إضافة العناصر المحددة للإرجاع
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* عناصر الإرجاع */}
      {formData.items && formData.items.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>عناصر الإرجاع</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-2">المنتج</th>
                    <th className="text-right p-2">الكمية</th>
                    <th className="text-right p-2">السعر</th>
                    <th className="text-right p-2">المجموع</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.items.map((item, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2">{item.product_name}</td>
                      <td className="p-2">{item.quantity}</td>
                      <td className="p-2">{formatCurrency(item.unit_price)}</td>
                      <td className="p-2 font-semibold">{formatCurrency(item.total_price)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex justify-between text-lg font-bold">
                <span>إجمالي مبلغ الإرجاع:</span>
                <span>{formatCurrency(formData.total_amount || 0)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* تفاصيل الإرجاع */}
      <Card>
        <CardHeader>
          <CardTitle>تفاصيل إضافية</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>ملاحظات الإرجاع</Label>
            <Textarea
              placeholder="أدخل أي ملاحظات إضافية حول الإرجاع..."
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>طريقة الإرجاع</Label>
              <Select
                value={formData.payment_method}
                onValueChange={(value) => setFormData(prev => ({ ...prev, payment_method: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">نقداً</SelectItem>
                  <SelectItem value="card">بطاقة</SelectItem>
                  <SelectItem value="credit">خصم من الحساب</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>المبلغ المسترد</Label>
              <Input
                type="number"
                step="0.01"
                value={formData.paid_amount}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  paid_amount: parseFloat(e.target.value) || 0,
                  remaining_amount: (prev.total_amount || 0) - (parseFloat(e.target.value) || 0)
                }))}
              />
            </div>
          </div>

          <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
            <span>حالة الإرجاع:</span>
            <Badge variant={
              formData.payment_status === 'paid' ? 'default' :
              formData.payment_status === 'partial' ? 'secondary' : 'destructive'
            }>
              {formData.payment_status === 'paid' ? 'مكتمل' :
               formData.payment_status === 'partial' ? 'جزئي' : 'معلق'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* أزرار الإجراءات */}
      <div className="flex justify-end space-x-4 space-x-reverse">
        <Button variant="outline">
          <Print className="w-4 h-4 ml-2" />
          طباعة
        </Button>

        <Button onClick={handleSave}>
          <Save className="w-4 h-4 ml-2" />
          حفظ فاتورة الإرجاع
        </Button>
      </div>
    </div>
  );
};

export default ReturnInvoice;
