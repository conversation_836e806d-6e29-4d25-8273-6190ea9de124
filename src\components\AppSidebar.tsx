
import {
  Home,
  ShoppingCart,
  Package,
  Users,
  FileText,
  TrendingUp,
  Settings,
  Store,
  Truck,
  Receipt,
  Warehouse,
  ChevronDown,
  LogOut,
  BarChart3,
  Zap
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar";
import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const menuItems = [
  {
    title: "لوحة التحكم",
    url: "/",
    icon: Home,
    color: "text-blue-600"
  },
  {
    title: "لوحة التحكم المتقدمة",
    url: "/dashboard",
    icon: BarChart3,
    color: "text-blue-500"
  },
  {
    title: "نقطة البيع",
    url: "/pos",
    icon: ShoppingCart,
    color: "text-green-600"
  },
  {
    title: "إدارة المنتجات",
    url: "/products",
    icon: Package,
    color: "text-purple-600"
  },
  {
    title: "إدارة العملاء",
    url: "/customers",
    icon: Users,
    color: "text-orange-600"
  },
  {
    title: "إدارة الموردين",
    url: "/suppliers",
    icon: Truck,
    color: "text-teal-600"
  },
  {
    title: "الفواتير",
    url: "/invoices",
    icon: Receipt,
    color: "text-red-600"
  },
  {
    title: "نظام الفواتير المتقدم",
    url: "/invoices-new",
    icon: Zap,
    color: "text-red-500"
  },
  {
    title: "المخزون",
    url: "/inventory",
    icon: Warehouse,
    color: "text-indigo-600"
  },
  {
    title: "التقارير",
    url: "/reports",
    icon: TrendingUp,
    color: "text-emerald-600"
  },
  {
    title: "الإعدادات",
    url: "/settings",
    icon: Settings,
    color: "text-gray-600"
  },
];

export function AppSidebar() {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeItem, setActiveItem] = useState(location.pathname);

  useEffect(() => {
    setActiveItem(location.pathname);
  }, [location.pathname]);

  const handleNavigation = (url: string, title: string) => {
    setActiveItem(url);
    navigate(url);
    console.log(`التنقل إلى: ${title}`);
  };

  return (
    <Sidebar side="right" className="border-l border-gray-200 bg-white shadow-lg">
      <SidebarHeader className="p-6 border-b border-gray-100 bg-gradient-to-l from-primary to-primary-600">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-md">
            <Store className="w-7 h-7 text-primary" />
          </div>
          <div className="text-white">
            <h2 className="text-xl font-bold">نظام إدارة المتاجر</h2>
            <p className="text-sm opacity-90">الإصدار 2.0</p>
          </div>
        </div>
      </SidebarHeader>
      
      <SidebarContent className="p-4 bg-gray-50">
        <SidebarGroup>
          <SidebarGroupLabel className="text-gray-700 font-bold mb-4 text-base">
            القائمة الرئيسية
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    className={`w-full justify-start rounded-xl transition-all duration-300 cursor-pointer group relative overflow-hidden ${
                      activeItem === item.url 
                        ? 'bg-white text-primary shadow-lg scale-105 border border-primary/20' 
                        : 'hover:bg-white hover:shadow-md text-gray-700 hover:scale-102'
                    }`}
                    onClick={() => handleNavigation(item.url, item.title)}
                  >
                    <div className="flex items-center gap-4 p-4 w-full relative z-10">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                        activeItem === item.url 
                          ? 'bg-primary/10' 
                          : 'bg-gray-100 group-hover:bg-primary/10'
                      } transition-colors duration-300`}>
                        <item.icon className={`w-5 h-5 ${
                          activeItem === item.url 
                            ? 'text-primary' 
                            : `${item.color} group-hover:text-primary`
                        } transition-colors duration-300`} />
                      </div>
                      <span className="font-semibold text-sm">{item.title}</span>
                      {activeItem === item.url && (
                        <div className="absolute left-0 top-0 w-1 h-full bg-primary rounded-r-full"></div>
                      )}
                    </div>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="p-4 border-t border-gray-200 bg-white">
        <div className="flex items-center gap-3 p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer">
          <Avatar className="w-10 h-10">
            <AvatarImage src="/placeholder.svg" />
            <AvatarFallback className="bg-primary text-white font-bold">أد</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <p className="font-semibold text-sm text-gray-900">المدير العام</p>
            <p className="text-xs text-gray-500"><EMAIL></p>
          </div>
          <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-400 hover:text-red-500">
            <LogOut className="w-4 h-4" />
          </Button>
        </div>
        <div className="text-center text-xs text-gray-400 mt-3">
          <p>© 2024 نظام إدارة المتاجر</p>
          <p>جميع الحقوق محفوظة</p>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
