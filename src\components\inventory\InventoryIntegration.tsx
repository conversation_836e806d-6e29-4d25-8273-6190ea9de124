import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Package, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  TrendingDown,
  TrendingUp,
  Warehouse,
  ShoppingCart,
  Eye
} from 'lucide-react';
import { Product, Invoice, InvoiceItem } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { toast } from '@/hooks/use-toast';

interface InventoryIntegrationProps {
  products: Product[];
  invoices: Invoice[];
  onUpdateProductStock: (productId: string, newStock: number) => Promise<void>;
  onCreateInventoryMovement: (movement: InventoryMovement) => Promise<void>;
}

interface InventoryMovement {
  product_id: string;
  movement_type: 'in' | 'out';
  quantity: number;
  reference_type: 'invoice' | 'adjustment' | 'transfer';
  reference_id?: string;
  notes?: string;
}

interface StockAlert {
  product: Product;
  type: 'low_stock' | 'out_of_stock' | 'overstock';
  message: string;
  severity: 'low' | 'medium' | 'high';
}

interface ProductMovement {
  product: Product;
  salesQuantity: number;
  purchaseQuantity: number;
  currentStock: number;
  projectedStock: number;
  turnoverRate: number;
}

const InventoryIntegration: React.FC<InventoryIntegrationProps> = ({
  products,
  invoices,
  onUpdateProductStock,
  onCreateInventoryMovement
}) => {
  const [stockAlerts, setStockAlerts] = useState<StockAlert[]>([]);
  const [productMovements, setProductMovements] = useState<ProductMovement[]>([]);
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [adjustmentQuantity, setAdjustmentQuantity] = useState(0);
  const [adjustmentNotes, setAdjustmentNotes] = useState('');

  useEffect(() => {
    generateStockAlerts();
    calculateProductMovements();
  }, [products, invoices]);

  // توليد تنبيهات المخزون
  const generateStockAlerts = () => {
    const alerts: StockAlert[] = [];

    products.forEach(product => {
      // تنبيه نفاد المخزون
      if (product.stock <= 0) {
        alerts.push({
          product,
          type: 'out_of_stock',
          message: 'المنتج غير متوفر في المخزون',
          severity: 'high'
        });
      }
      // تنبيه انخفاض المخزون
      else if (product.stock <= product.minStock) {
        alerts.push({
          product,
          type: 'low_stock',
          message: `المخزون منخفض (${product.stock} متبقي، الحد الأدنى ${product.minStock})`,
          severity: 'medium'
        });
      }
      // تنبيه فائض المخزون (أكثر من 10 أضعاف الحد الأدنى)
      else if (product.stock > product.minStock * 10) {
        alerts.push({
          product,
          type: 'overstock',
          message: `مخزون زائد (${product.stock} متوفر)`,
          severity: 'low'
        });
      }
    });

    setStockAlerts(alerts.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    }));
  };

  // حساب حركة المنتجات
  const calculateProductMovements = () => {
    const movements: ProductMovement[] = [];
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    products.forEach(product => {
      let salesQuantity = 0;
      let purchaseQuantity = 0;

      // حساب المبيعات والمشتريات في آخر 30 يوم
      invoices
        .filter(invoice => new Date(invoice.invoice_date) >= thirtyDaysAgo)
        .forEach(invoice => {
          invoice.items?.forEach(item => {
            if (item.product_id === product.id) {
              if (invoice.type === 'sales') {
                salesQuantity += item.quantity;
              } else if (invoice.type === 'purchase') {
                purchaseQuantity += item.quantity;
              }
            }
          });
        });

      // حساب معدل الدوران (مرات البيع في الشهر)
      const turnoverRate = salesQuantity / Math.max(product.stock, 1);
      
      // توقع المخزون بناءً على معدل البيع
      const projectedStock = Math.max(0, product.stock - (salesQuantity * 2)); // توقع لشهرين

      movements.push({
        product,
        salesQuantity,
        purchaseQuantity,
        currentStock: product.stock,
        projectedStock,
        turnoverRate
      });
    });

    setProductMovements(movements.sort((a, b) => b.turnoverRate - a.turnoverRate));
  };

  // تحديث المخزون يدوياً
  const handleStockAdjustment = async () => {
    if (!selectedProduct || adjustmentQuantity === 0) {
      toast({
        title: "خطأ",
        description: "يرجى اختيار منتج وإدخال كمية التعديل",
        variant: "destructive"
      });
      return;
    }

    setIsUpdating(true);
    try {
      const newStock = selectedProduct.stock + adjustmentQuantity;
      
      if (newStock < 0) {
        toast({
          title: "خطأ",
          description: "لا يمكن أن يكون المخزون أقل من صفر",
          variant: "destructive"
        });
        return;
      }

      await onUpdateProductStock(selectedProduct.id, newStock);
      
      // تسجيل حركة المخزون
      await onCreateInventoryMovement({
        product_id: selectedProduct.id,
        movement_type: adjustmentQuantity > 0 ? 'in' : 'out',
        quantity: Math.abs(adjustmentQuantity),
        reference_type: 'adjustment',
        notes: adjustmentNotes || 'تعديل يدوي للمخزون'
      });

      setSelectedProduct(null);
      setAdjustmentQuantity(0);
      setAdjustmentNotes('');
      
      toast({
        title: "تم التحديث",
        description: "تم تحديث المخزون بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث المخزون",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // تحديث المخزون تلقائياً من الفواتير
  const syncInventoryFromInvoices = async () => {
    setIsUpdating(true);
    try {
      // هذه الوظيفة ستقوم بمراجعة جميع الفواتير وتحديث المخزون
      // في التطبيق الحقيقي، ستكون هذه عملية معقدة تتطلب مراجعة دقيقة
      
      toast({
        title: "تم المزامنة",
        description: "تم مزامنة المخزون مع الفواتير بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في مزامنة المخزون",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'high':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'medium':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'low':
        return <CheckCircle className="w-5 h-5 text-blue-500" />;
      default:
        return <AlertTriangle className="w-5 h-5" />;
    }
  };

  const getAlertVariant = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'default';
      case 'low':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStockLevel = (current: number, min: number) => {
    if (current <= 0) return { level: 0, color: 'bg-red-500', label: 'نفد' };
    if (current <= min) return { level: 25, color: 'bg-yellow-500', label: 'منخفض' };
    if (current <= min * 2) return { level: 50, color: 'bg-blue-500', label: 'متوسط' };
    return { level: 100, color: 'bg-green-500', label: 'جيد' };
  };

  return (
    <div className="space-y-6 p-6" dir="rtl">
      {/* الإحصائيات السريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المنتجات</p>
                <p className="text-2xl font-bold">{products.length}</p>
              </div>
              <Package className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">تنبيهات المخزون</p>
                <p className="text-2xl font-bold text-red-600">{stockAlerts.length}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">قيمة المخزون</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(products.reduce((sum, p) => sum + (p.stock * p.purchasePrice), 0))}
                </p>
              </div>
              <Warehouse className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">منتجات نافدة</p>
                <p className="text-2xl font-bold text-orange-600">
                  {products.filter(p => p.stock <= 0).length}
                </p>
              </div>
              <XCircle className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* تنبيهات المخزون */}
      {stockAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5" />
              تنبيهات المخزون ({stockAlerts.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {stockAlerts.slice(0, 10).map((alert, index) => (
              <Alert key={index} variant={getAlertVariant(alert.severity)}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getAlertIcon(alert.severity)}
                    <div>
                      <div className="font-medium">{alert.product.name}</div>
                      <AlertDescription>{alert.message}</AlertDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">
                      {alert.product.stock} متوفر
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedProduct(alert.product)}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </Alert>
            ))}
            
            {stockAlerts.length > 10 && (
              <div className="text-center text-gray-500">
                و {stockAlerts.length - 10} تنبيه آخر...
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* أدوات إدارة المخزون */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* تعديل المخزون */}
        <Card>
          <CardHeader>
            <CardTitle>تعديل المخزون</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>اختيار المنتج</Label>
              <select
                className="w-full p-2 border rounded-lg"
                value={selectedProduct?.id || ''}
                onChange={(e) => {
                  const product = products.find(p => p.id === e.target.value);
                  setSelectedProduct(product || null);
                }}
              >
                <option value="">اختر منتج...</option>
                {products.map(product => (
                  <option key={product.id} value={product.id}>
                    {product.name} (متوفر: {product.stock})
                  </option>
                ))}
              </select>
            </div>

            {selectedProduct && (
              <>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span>المخزون الحالي:</span>
                    <Badge variant="outline">{selectedProduct.stock}</Badge>
                  </div>
                </div>

                <div>
                  <Label>كمية التعديل (+ للإضافة، - للخصم)</Label>
                  <Input
                    type="number"
                    value={adjustmentQuantity}
                    onChange={(e) => setAdjustmentQuantity(parseInt(e.target.value) || 0)}
                    placeholder="0"
                  />
                </div>

                <div>
                  <Label>ملاحظات</Label>
                  <Input
                    value={adjustmentNotes}
                    onChange={(e) => setAdjustmentNotes(e.target.value)}
                    placeholder="سبب التعديل..."
                  />
                </div>

                {adjustmentQuantity !== 0 && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span>المخزون بعد التعديل:</span>
                      <Badge variant="outline">
                        {selectedProduct.stock + adjustmentQuantity}
                      </Badge>
                    </div>
                  </div>
                )}

                <Button
                  onClick={handleStockAdjustment}
                  disabled={isUpdating || adjustmentQuantity === 0}
                  className="w-full"
                >
                  {isUpdating ? (
                    <RefreshCw className="w-4 h-4 ml-2 animate-spin" />
                  ) : (
                    <Package className="w-4 h-4 ml-2" />
                  )}
                  تطبيق التعديل
                </Button>
              </>
            )}
          </CardContent>
        </Card>

        {/* مزامنة المخزون */}
        <Card>
          <CardHeader>
            <CardTitle>مزامنة المخزون</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-gray-600">
              قم بمزامنة المخزون مع الفواتير لضمان دقة البيانات
            </div>

            <Alert>
              <AlertTriangle className="w-4 h-4" />
              <AlertDescription>
                تأكد من مراجعة جميع الفواتير قبل المزامنة. هذه العملية ستؤثر على جميع أرصدة المخزون.
              </AlertDescription>
            </Alert>

            <Button
              onClick={syncInventoryFromInvoices}
              disabled={isUpdating}
              variant="outline"
              className="w-full"
            >
              {isUpdating ? (
                <RefreshCw className="w-4 h-4 ml-2 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4 ml-2" />
              )}
              مزامنة مع الفواتير
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* تحليل حركة المنتجات */}
      <Card>
        <CardHeader>
          <CardTitle>تحليل حركة المنتجات (آخر 30 يوم)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {productMovements.slice(0, 10).map((movement, index) => {
              const stockLevel = getStockLevel(movement.currentStock, movement.product.minStock);
              
              return (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-4">
                      <div>
                        <div className="font-medium">{movement.product.name}</div>
                        <div className="text-sm text-gray-500">
                          مبيعات: {movement.salesQuantity} | مشتريات: {movement.purchaseQuantity}
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-2">
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>مستوى المخزون</span>
                        <span>{stockLevel.label}</span>
                      </div>
                      <Progress value={stockLevel.level} className="h-2" />
                    </div>
                  </div>

                  <div className="text-right ml-4">
                    <div className="font-semibold">
                      {movement.currentStock} متوفر
                    </div>
                    <div className="text-sm text-gray-500">
                      معدل الدوران: {movement.turnoverRate.toFixed(1)}x
                    </div>
                    <div className="text-sm text-gray-500">
                      توقع: {movement.projectedStock}
                    </div>
                  </div>

                  <div className="flex items-center">
                    {movement.turnoverRate > 2 ? (
                      <TrendingUp className="w-5 h-5 text-green-500" />
                    ) : movement.turnoverRate < 0.5 ? (
                      <TrendingDown className="w-5 h-5 text-red-500" />
                    ) : (
                      <Package className="w-5 h-5 text-gray-500" />
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default InventoryIntegration;
