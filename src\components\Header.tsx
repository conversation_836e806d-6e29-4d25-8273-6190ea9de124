
import React, { useState } from 'react';
import { <PERSON>, <PERSON>, Sun, <PERSON>, MessageSquare } from 'lucide-react';

export function Header() {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);

  return (
    <header style={{
      background: isDarkMode
        ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
        : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
      borderBottom: isDarkMode
        ? '1px solid rgba(255,255,255,0.1)'
        : '1px solid rgba(0,0,0,0.1)',
      padding: '1rem 2rem',
      backdropFilter: 'blur(20px)',
      boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
      position: 'sticky',
      top: 0,
      zIndex: 100
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        {/* الجانب الأيسر - العنوان والتاريخ */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '1.5rem'
        }}>
          <div>
            <h1 style={{
              fontSize: '1.75rem',
              fontWeight: '700',
              margin: 0,
              background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              لوحة التحكم العصرية
            </h1>
            <p style={{
              fontSize: '0.875rem',
              color: isDarkMode ? '#94a3b8' : '#64748b',
              margin: '0.25rem 0 0 0',
              fontWeight: '500'
            }}>
              {new Date().toLocaleDateString('ar-DZ', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })} • {new Date().toLocaleTimeString('ar-DZ', {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          </div>
        </div>

        {/* الجانب الأيمن - الأدوات */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '1rem'
        }}>
          {/* شريط البحث السريع */}
          <div style={{
            position: 'relative',
            display: 'block' // سيكون مرئي دائماً
          }}>
            <Search
              size={18}
              style={{
                position: 'absolute',
                right: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                color: isDarkMode ? '#64748b' : '#94a3b8'
              }}
            />
            <input
              type="text"
              placeholder="البحث السريع..."
              style={{
                width: '250px',
                padding: '8px 40px 8px 12px',
                border: isDarkMode
                  ? '1px solid rgba(255,255,255,0.1)'
                  : '1px solid rgba(0,0,0,0.1)',
                borderRadius: '12px',
                background: isDarkMode
                  ? 'rgba(255,255,255,0.05)'
                  : 'rgba(0,0,0,0.02)',
                color: isDarkMode ? '#f1f5f9' : '#1e293b',
                fontSize: '0.875rem',
                outline: 'none',
                transition: 'all 0.3s ease'
              }}
            />
          </div>

          {/* أزرار الأدوات */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            {/* زر الوضع الداكن */}
            <button
              onClick={() => setIsDarkMode(!isDarkMode)}
              style={{
                width: '44px',
                height: '44px',
                border: 'none',
                borderRadius: '12px',
                background: isDarkMode
                  ? 'rgba(255,255,255,0.1)'
                  : 'rgba(0,0,0,0.05)',
                color: isDarkMode ? '#f1f5f9' : '#1e293b',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
            </button>

            {/* زر الرسائل */}
            <button
              style={{
                width: '44px',
                height: '44px',
                border: 'none',
                borderRadius: '12px',
                background: isDarkMode
                  ? 'rgba(255,255,255,0.1)'
                  : 'rgba(0,0,0,0.05)',
                color: isDarkMode ? '#f1f5f9' : '#1e293b',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative'
              }}
            >
              <MessageSquare size={20} />
              <span style={{
                position: 'absolute',
                top: '8px',
                right: '8px',
                width: '8px',
                height: '8px',
                background: '#10b981',
                borderRadius: '50%'
              }} />
            </button>

            {/* زر التنبيهات */}
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              style={{
                width: '44px',
                height: '44px',
                border: 'none',
                borderRadius: '12px',
                background: isDarkMode
                  ? 'rgba(255,255,255,0.1)'
                  : 'rgba(0,0,0,0.05)',
                color: isDarkMode ? '#f1f5f9' : '#1e293b',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative'
              }}
            >
              <Bell size={20} />
              <span style={{
                position: 'absolute',
                top: '6px',
                right: '6px',
                width: '18px',
                height: '18px',
                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                color: 'white',
                fontSize: '0.75rem',
                fontWeight: '600',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 8px rgba(239, 68, 68, 0.3)'
              }}>
                5
              </span>
            </button>

            {/* فاصل */}
            <div style={{
              width: '1px',
              height: '24px',
              background: isDarkMode
                ? 'rgba(255,255,255,0.1)'
                : 'rgba(0,0,0,0.1)',
              margin: '0 0.5rem'
            }} />

            {/* ملف المستخدم */}
            <button
              onClick={() => setShowProfile(!showProfile)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                padding: '0.5rem 1rem',
                border: 'none',
                borderRadius: '12px',
                background: isDarkMode
                  ? 'rgba(255,255,255,0.1)'
                  : 'rgba(0,0,0,0.05)',
                color: isDarkMode ? '#f1f5f9' : '#1e293b',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                position: 'relative'
              }}
            >
              <div style={{
                width: '36px',
                height: '36px',
                background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                borderRadius: '10px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '16px',
                boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)'
              }}>
                👨‍💼
              </div>
              <div style={{ textAlign: 'right' }}>
                <p style={{
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  margin: 0
                }}>
                  أحمد محمد
                </p>
                <p style={{
                  fontSize: '0.75rem',
                  opacity: 0.7,
                  margin: 0
                }}>
                  مدير النظام
                </p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
