import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Plus, 
  Search, 
  Package, 
  Scan, 
  X, 
  Check,
  AlertTriangle,
  Star,
  Clock,
  TrendingUp
} from 'lucide-react';
import { Product, InvoiceItem } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { toast } from '@/hooks/use-toast';
import BarcodeScanner from './BarcodeScanner';

interface DynamicProductSelectorProps {
  products: Product[];
  recentProducts?: Product[];
  popularProducts?: Product[];
  onProductSelected: (product: Product, quantity: number) => void;
  onCreateNewProduct?: (productData: Partial<Product>) => Promise<Product>;
  allowCreateNew?: boolean;
  placeholder?: string;
  maxRecentItems?: number;
}

interface QuickAddForm {
  name: string;
  barcode: string;
  salePrice: number;
  purchasePrice: number;
  stock: number;
  minStock: number;
}

const DynamicProductSelector: React.FC<DynamicProductSelectorProps> = ({
  products,
  recentProducts = [],
  popularProducts = [],
  onProductSelected,
  onCreateNewProduct,
  allowCreateNew = false,
  placeholder = "ابحث عن منتج أو امسح الباركود...",
  maxRecentItems = 5
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [quickAddForm, setQuickAddForm] = useState<QuickAddForm>({
    name: '',
    barcode: '',
    salePrice: 0,
    purchasePrice: 0,
    stock: 0,
    minStock: 5
  });
  const [quantity, setQuantity] = useState(1);
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // تصفية المنتجات بناءً على البحث
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredProducts([]);
      setShowSuggestions(false);
      return;
    }

    const searchLower = searchTerm.toLowerCase();
    const filtered = products.filter(product =>
      product.name.toLowerCase().includes(searchLower) ||
      product.barcode?.toLowerCase().includes(searchLower) ||
      product.sku?.toLowerCase().includes(searchLower) ||
      product.category?.toLowerCase().includes(searchLower)
    ).slice(0, 10);

    setFilteredProducts(filtered);
    setShowSuggestions(true);
    setSelectedIndex(-1);
  }, [searchTerm, products]);

  // معالجة اختيار المنتج
  const handleProductSelect = (product: Product) => {
    onProductSelected(product, quantity);
    setSearchTerm('');
    setShowSuggestions(false);
    setQuantity(1);
    
    toast({
      title: "تم إضافة المنتج",
      description: `${product.name} - الكمية: ${quantity}`
    });
  };

  // معالجة البحث بالباركود
  const handleBarcodeFound = (product: Product) => {
    handleProductSelect(product);
  };

  // معالجة لوحة المفاتيح
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || filteredProducts.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < filteredProducts.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : filteredProducts.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < filteredProducts.length) {
          handleProductSelect(filteredProducts[selectedIndex]);
        } else if (filteredProducts.length === 1) {
          handleProductSelect(filteredProducts[0]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  // إنشاء منتج جديد
  const handleCreateNewProduct = async () => {
    if (!onCreateNewProduct) return;

    if (!quickAddForm.name.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم المنتج",
        variant: "destructive"
      });
      return;
    }

    try {
      const newProduct = await onCreateNewProduct({
        name: quickAddForm.name,
        barcode: quickAddForm.barcode || undefined,
        salePrice: quickAddForm.salePrice,
        purchasePrice: quickAddForm.purchasePrice,
        stock: quickAddForm.stock,
        minStock: quickAddForm.minStock,
        category: 'عام',
        unit: 'قطعة'
      });

      handleProductSelect(newProduct);
      setIsCreateDialogOpen(false);
      setQuickAddForm({
        name: '',
        barcode: '',
        salePrice: 0,
        purchasePrice: 0,
        stock: 0,
        minStock: 5
      });

      toast({
        title: "تم الإنشاء",
        description: "تم إنشاء المنتج وإضافته للفاتورة"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في إنشاء المنتج",
        variant: "destructive"
      });
    }
  };

  // الحصول على المنتجات الحديثة المحدودة
  const displayRecentProducts = recentProducts.slice(0, maxRecentItems);

  return (
    <div className="space-y-4" dir="rtl">
      {/* حقل البحث الرئيسي */}
      <div className="relative">
        <Label>البحث عن منتج</Label>
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute right-3 top-3 w-4 h-4 text-gray-400" />
            <Input
              ref={searchInputRef}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => searchTerm && setShowSuggestions(true)}
              placeholder={placeholder}
              className="pr-10"
            />
            
            {/* قائمة الاقتراحات */}
            {showSuggestions && filteredProducts.length > 0 && (
              <div
                ref={suggestionsRef}
                className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border rounded-lg shadow-lg max-h-64 overflow-y-auto"
              >
                {filteredProducts.map((product, index) => (
                  <div
                    key={product.id}
                    className={`p-3 cursor-pointer border-b last:border-b-0 hover:bg-gray-50 ${
                      index === selectedIndex ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => handleProductSelect(product)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-gray-500 flex items-center gap-2">
                          {product.barcode && (
                            <span className="font-mono">{product.barcode}</span>
                          )}
                          <span>•</span>
                          <span>{formatCurrency(product.salePrice)}</span>
                          <span>•</span>
                          <span>متوفر: {product.stock}</span>
                        </div>
                      </div>
                      <Badge variant={product.stock > 0 ? 'default' : 'destructive'}>
                        {product.stock > 0 ? 'متوفر' : 'نفد'}
                      </Badge>
                    </div>
                  </div>
                ))}
                
                {/* خيار إنشاء منتج جديد */}
                {allowCreateNew && onCreateNewProduct && searchTerm.trim() && (
                  <div
                    className="p-3 cursor-pointer border-t bg-blue-50 hover:bg-blue-100"
                    onClick={() => {
                      setQuickAddForm(prev => ({ ...prev, name: searchTerm }));
                      setIsCreateDialogOpen(true);
                      setShowSuggestions(false);
                    }}
                  >
                    <div className="flex items-center gap-2 text-blue-600">
                      <Plus className="w-4 h-4" />
                      <span>إنشاء منتج جديد: "{searchTerm}"</span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* حقل الكمية */}
          <div className="w-24">
            <Label>الكمية</Label>
            <Input
              type="number"
              min="1"
              value={quantity}
              onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
              className="text-center"
            />
          </div>
        </div>
      </div>

      {/* مسح الباركود */}
      <BarcodeScanner
        products={products}
        onProductFound={handleBarcodeFound}
        placeholder="امسح باركود المنتج..."
      />

      {/* المنتجات الحديثة */}
      {displayRecentProducts.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Clock className="w-4 h-4" />
              المنتجات الحديثة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {displayRecentProducts.map((product) => (
                <div
                  key={product.id}
                  className="flex items-center justify-between p-2 border rounded cursor-pointer hover:bg-gray-50"
                  onClick={() => handleProductSelect(product)}
                >
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{product.name}</div>
                    <div className="text-sm text-gray-500">
                      {formatCurrency(product.salePrice)}
                    </div>
                  </div>
                  <Badge variant="outline" className="ml-2">
                    {product.stock}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* المنتجات الشائعة */}
      {popularProducts.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              المنتجات الشائعة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {popularProducts.slice(0, 6).map((product) => (
                <div
                  key={product.id}
                  className="flex items-center justify-between p-2 border rounded cursor-pointer hover:bg-gray-50"
                  onClick={() => handleProductSelect(product)}
                >
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate flex items-center gap-1">
                      <Star className="w-3 h-3 text-yellow-500" />
                      {product.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatCurrency(product.salePrice)}
                    </div>
                  </div>
                  <Badge variant="outline" className="ml-2">
                    {product.stock}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* نافذة إنشاء منتج جديد */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إنشاء منتج جديد</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>اسم المنتج *</Label>
              <Input
                value={quickAddForm.name}
                onChange={(e) => setQuickAddForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="أدخل اسم المنتج..."
              />
            </div>

            <div>
              <Label>الباركود</Label>
              <Input
                value={quickAddForm.barcode}
                onChange={(e) => setQuickAddForm(prev => ({ ...prev, barcode: e.target.value }))}
                placeholder="اختياري..."
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>سعر البيع *</Label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={quickAddForm.salePrice}
                  onChange={(e) => setQuickAddForm(prev => ({ ...prev, salePrice: parseFloat(e.target.value) || 0 }))}
                />
              </div>
              <div>
                <Label>سعر الشراء</Label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={quickAddForm.purchasePrice}
                  onChange={(e) => setQuickAddForm(prev => ({ ...prev, purchasePrice: parseFloat(e.target.value) || 0 }))}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>الكمية الحالية</Label>
                <Input
                  type="number"
                  min="0"
                  value={quickAddForm.stock}
                  onChange={(e) => setQuickAddForm(prev => ({ ...prev, stock: parseInt(e.target.value) || 0 }))}
                />
              </div>
              <div>
                <Label>الحد الأدنى</Label>
                <Input
                  type="number"
                  min="0"
                  value={quickAddForm.minStock}
                  onChange={(e) => setQuickAddForm(prev => ({ ...prev, minStock: parseInt(e.target.value) || 0 }))}
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Button onClick={handleCreateNewProduct} className="flex-1">
                <Package className="w-4 h-4 ml-2" />
                إنشاء وإضافة
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsCreateDialogOpen(false)}
                className="flex-1"
              >
                إلغاء
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* رسالة عدم وجود نتائج */}
      {searchTerm.trim() && filteredProducts.length === 0 && showSuggestions && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-yellow-800">
              <AlertTriangle className="w-4 h-4" />
              <span>لم يتم العثور على منتجات تطابق البحث</span>
            </div>
            {allowCreateNew && onCreateNewProduct && (
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => {
                  setQuickAddForm(prev => ({ ...prev, name: searchTerm }));
                  setIsCreateDialogOpen(true);
                }}
              >
                <Plus className="w-4 h-4 ml-2" />
                إنشاء منتج جديد
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DynamicProductSelector;
