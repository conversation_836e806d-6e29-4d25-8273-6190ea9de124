// filepath: c:\Users\<USER>\Desktop\dz\dz-store-manager-web\src\utils\pdf-generator.ts
import { jsPDF } from "jspdf";
import { formatCurrency } from "./currency";

// تحميل jspdf-autotable
import('jspdf-autotable').then(() => {
  console.log('jspdf-autotable loaded');
}).catch(err => {
  console.error('Failed to load jspdf-autotable:', err);
});

// دالة لتحويل النص العربي إلى نص قابل للعرض في PDF
const convertArabicText = (text: string): string => {
  // قاموس ترجمة مؤقت للنصوص الأساسية
  const translations: { [key: string]: string } = {
    'معلومات العميل': 'Customer Information',
    'اسم العميل': 'Customer Name',
    'رقم الهاتف': 'Phone Number',
    'البريد الإلكتروني': 'Email',
    'عناصر الفاتورة': 'Invoice Items',
    'المنتج': 'Product',
    'الكمية': 'Quantity',
    'السعر': 'Price',
    'المجموع': 'Total',
    'المجموع الكلي': 'Total Amount',
    'المبلغ المدفوع': 'Paid Amount',
    'المتبقي': 'Remaining',
    'طريقة الدفع': 'Payment Method',
    'حالة الدفع': 'Payment Status',
    'نقدي': 'Cash',
    'بطاقة': 'Card',
    'مدفوع': 'Paid',
    'جزئي': 'Partial',
    'غير مدفوع': 'Unpaid',
    'ملاحظات': 'Notes'
  };

  return translations[text] || text;
};

// إضافة التعريفات للـ TypeScript
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: {
      head?: string[][];
      body: (string | number)[][];
      startY?: number;
      theme?: string;
      headStyles?: object;
      bodyStyles?: object;
      styles?: object;
      columnStyles?: object;
      didDrawPage?: () => void;
    }) => jsPDF;
    lastAutoTable: { finalY: number };
  }
}

interface InvoiceType {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date?: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string | null;
  notes?: string | null;
  customers?: {
    name: string;
    phone?: string;
    email?: string;
  };
  invoice_items?: Array<{
    products?: {
      name: string;
      code?: string;
    };
    quantity: number;
    unit_price: number;
    total_price: number;
  }>;
}

export const generateInvoicePDF = async (invoice: InvoiceType): Promise<jsPDF> => {
  // التأكد من تحميل jspdf-autotable
  try {
    await import('jspdf-autotable');
  } catch (error) {
    console.error('Failed to load jspdf-autotable:', error);
    throw new Error('فشل في تحميل مكتبة إنشاء الجداول');
  }

  // إنشاء مستند PDF جديد
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
  });

  // إضافة خط عربي (مؤقت<|im_start|> - استخدام خط افتراضي)
  try {
    // محاولة تعيين خط يدعم العربية
    doc.setFont('helvetica');
  } catch (error) {
    console.warn('Could not set Arabic font, using default');
  }
  // نستخدم الخط الافتراضي
  doc.setFont('Helvetica');

  // الهوامش
  const margin = 10;
  const pageWidth = doc.internal.pageSize.width;
  
  // إضافة العنوان
  doc.setFontSize(24);
  doc.text("DZ - Store Manager", pageWidth - margin, margin + 10, { align: "right" });
  
  // معلومات الفاتورة
  doc.setFontSize(12);
  doc.text(`Invoice #: ${invoice.invoice_number}`, pageWidth - margin, margin + 20, { align: "right" });
  doc.text(`Date: ${new Date(invoice.invoice_date).toLocaleDateString()}`, pageWidth - margin, margin + 25, { align: "right" });
  
  if (invoice.due_date) {
    doc.text(`Due Date: ${new Date(invoice.due_date).toLocaleDateString()}`, pageWidth - margin, margin + 30, { align: "right" });
  }
  
  // معلومات العميل
  doc.setFontSize(14);
  doc.text(convertArabicText("معلومات العميل"), pageWidth - margin, margin + 40, { align: "right" });
  doc.setFontSize(12);
  doc.text(`${convertArabicText("اسم العميل")}: ${invoice.customers?.name || 'Not specified'}`, pageWidth - margin, margin + 45, { align: "right" });
    if (invoice.customers?.phone) {
    doc.text(`${convertArabicText("رقم الهاتف")}: ${invoice.customers.phone}`, pageWidth - margin, margin + 50, { align: "right" });
  }

  if (invoice.customers?.email) {
    doc.text(`${convertArabicText("البريد الإلكتروني")}: ${invoice.customers.email}`, pageWidth - margin, margin + 55, { align: "right" });
  }
  
  // جدول المنتجات (بدون autoTable)
  let currentY = margin + 65;

  // عنوان الجدول
  doc.setFontSize(14);
  doc.text(convertArabicText("عناصر الفاتورة"), pageWidth - margin, currentY, { align: "right" });
  currentY += 10;

  // رؤوس الجدول
  doc.setFontSize(12);
  doc.text(convertArabicText("المنتج"), pageWidth - margin, currentY, { align: "right" });
  doc.text(convertArabicText("الكمية"), pageWidth - margin - 60, currentY, { align: "right" });
  doc.text(convertArabicText("السعر"), pageWidth - margin - 90, currentY, { align: "right" });
  doc.text(convertArabicText("المجموع"), pageWidth - margin - 130, currentY, { align: "right" });
  currentY += 5;

  // خط تحت الرؤوس
  doc.line(margin, currentY, pageWidth - margin, currentY);
  currentY += 5;

  // عناصر الفاتورة
  doc.setFontSize(10);
  invoice.invoice_items?.forEach((item) => {
    doc.text(item.products?.name || 'Unknown', pageWidth - margin, currentY, { align: "right" });
    doc.text(item.quantity.toString(), pageWidth - margin - 60, currentY, { align: "right" });
    doc.text(formatCurrency(item.unit_price), pageWidth - margin - 90, currentY, { align: "right" });
    doc.text(formatCurrency(item.total_price), pageWidth - margin - 130, currentY, { align: "right" });
    currentY += 5;
  });

  // خط تحت العناصر
  currentY += 5;
  doc.line(margin, currentY, pageWidth - margin, currentY);

  // ملخص المبالغ
  let finalY = currentY + 10;

  doc.setFontSize(12);
  doc.text(`${convertArabicText("المجموع الكلي")}: ${formatCurrency(invoice.total_amount)}`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;

  doc.text(`${convertArabicText("المبلغ المدفوع")}: ${formatCurrency(invoice.paid_amount)}`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;
  doc.text(`${convertArabicText("المتبقي")}: ${formatCurrency(invoice.remaining_amount)}`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;

  doc.text(`${convertArabicText("طريقة الدفع")}: ${
    invoice.payment_method === 'cash' ? convertArabicText('نقدي') :
    invoice.payment_method === 'card' ? convertArabicText('بطاقة') :
    invoice.payment_method
  }`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;

  doc.text(`${convertArabicText("حالة الدفع")}: ${
    invoice.payment_status === 'paid' ? convertArabicText('مدفوع') :
    invoice.payment_status === 'partial' ? convertArabicText('جزئي') :
    convertArabicText('غير مدفوع')
  }`, pageWidth - margin, finalY, { align: "right" });
  finalY += 15;

  // ملاحظات (إن وجدت)
  if (invoice.notes) {
    doc.setFontSize(12);
    doc.text(`${convertArabicText("ملاحظات")}:`, pageWidth - margin, finalY, { align: "right" });
    finalY += 5;
    doc.text(invoice.notes, pageWidth - margin, finalY, { align: "right" });
  }
  // معلومات المتجر في أسفل الصفحة
  const shopInfo = "DZ Store Manager - All Rights Reserved";
  
  doc.setFontSize(10);
  doc.text(shopInfo, pageWidth / 2, doc.internal.pageSize.height - 20, { align: 'center' });
  
  // تصدير الملف
  return doc;
};

interface InvoiceFromDB {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date?: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string | null;
  notes?: string | null;
  customers?: {
    name: string;
    phone?: string;
    email?: string | null;
  };
  invoice_items?: Array<{
    quantity: number;
    unit_price: number;
    total_price: number;
    products?: {
      name: string;
    };
  }>;
}

export const downloadInvoicePDF = async (invoice: InvoiceFromDB): Promise<boolean> => {
  try {
    // تحويل الكائن إلى النوع المطلوب
    const invoiceData: InvoiceType = {
      id: invoice.id,
      invoice_number: invoice.invoice_number,
      invoice_date: invoice.invoice_date,
      due_date: invoice.due_date,
      total_amount: invoice.total_amount,
      paid_amount: invoice.paid_amount,
      remaining_amount: invoice.remaining_amount,
      payment_status: invoice.payment_status,
      payment_method: invoice.payment_method,
      notes: invoice.notes,
      customers: invoice.customers ? {
        name: invoice.customers.name,
        phone: invoice.customers.phone,
        email: invoice.customers.email
      } : undefined,
      invoice_items: invoice.invoice_items ? invoice.invoice_items.map((item) => ({
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        products: item.products ? {
          name: item.products.name
        } : undefined
      })) : undefined
    };
    
    const doc = await generateInvoicePDF(invoiceData);
    doc.save(`invoice-${invoice.invoice_number}.pdf`);
    return true;
  } catch (error) {
    console.error('Error generating PDF:', error);
    return false;
  }
};
