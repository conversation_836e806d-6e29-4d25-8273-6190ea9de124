// filepath: c:\Users\<USER>\Desktop\dz\dz-store-manager-web\src\utils\pdf-generator.ts
import { jsPDF } from "jspdf";
import { formatCurrency } from "./currency";

// تحميل jspdf-autotable
import('jspdf-autotable').then(() => {
  console.log('jspdf-autotable loaded');
}).catch(err => {
  console.error('Failed to load jspdf-autotable:', err);
});

// دالة لتحويل النص العربي إلى نص قابل للعرض في PDF (مطابق للطباعة)
const getArabicText = (key: string): string => {
  // نصوص عربية مطابقة لصفحة الطباعة
  const arabicTexts: { [key: string]: string } = {
    'company_name': 'دي زد - إدارة المخزون',
    'invoice': 'فاتورة',
    'date': 'تاريخ:',
    'customer': 'العميل:',
    'phone': 'رقم الهاتف:',
    'email': 'البريد الإلكتروني:',
    'product': 'المنتج',
    'quantity': 'الكمية',
    'price': 'السعر',
    'total': 'المجموع',
    'total_amount': 'المجموع:',
    'paid_amount': 'المدفوع:',
    'remaining': 'المتبقي:',
    'payment_method': 'طريقة الدفع:',
    'payment_status': 'حالة الدفع:',
    'cash': 'نقداً',
    'card': 'بطاقة ائتمان',
    'paid': 'مدفوع',
    'partial': 'مدفوع جزئياً',
    'unpaid': 'غير مدفوع',
    'notes': 'ملاحظات:',
    'thank_you': 'شكراً لك',
    'not_specified': 'غير محدد',
    'unknown': 'غير معروف',
    'currency': 'دج'
  };

  return arabicTexts[key] || key;
};

// إضافة التعريفات للـ TypeScript
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: {
      head?: string[][];
      body: (string | number)[][];
      startY?: number;
      theme?: string;
      headStyles?: object;
      bodyStyles?: object;
      styles?: object;
      columnStyles?: object;
      didDrawPage?: () => void;
    }) => jsPDF;
    lastAutoTable: { finalY: number };
  }
}

interface InvoiceType {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date?: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string | null;
  notes?: string | null;
  customers?: {
    name: string;
    phone?: string;
    email?: string;
  };
  invoice_items?: Array<{
    products?: {
      name: string;
      code?: string;
    };
    quantity: number;
    unit_price: number;
    total_price: number;
  }>;
}

export const generateInvoicePDF = async (invoice: InvoiceType): Promise<jsPDF> => {
  // التأكد من تحميل jspdf-autotable
  try {
    await import('jspdf-autotable');
  } catch (error) {
    console.error('Failed to load jspdf-autotable:', error);
    throw new Error('فشل في تحميل مكتبة إنشاء الجداول');
  }

  // إنشاء مستند PDF جديد
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
  });

  // إضافة خط عربي (مؤقت<|im_start|> - استخدام خط افتراضي)
  try {
    // محاولة تعيين خط يدعم العربية
    doc.setFont('helvetica');
  } catch (error) {
    console.warn('Could not set Arabic font, using default');
  }
  // نستخدم الخط الافتراضي
  doc.setFont('Helvetica');

  // الهوامش
  const margin = 15;
  const pageWidth = doc.internal.pageSize.width;

  // رأس الفاتورة (مطابق للطباعة العربية)
  doc.setFontSize(24);
  doc.text(getArabicText('company_name'), pageWidth - margin, margin + 10, { align: "right" });

  // معلومات الفاتورة في الجانب الأيسر
  doc.setFontSize(14);
  doc.text(`${getArabicText('invoice')} ${invoice.invoice_number}`, margin, margin + 10, { align: "left" });
  doc.setFontSize(12);
  doc.text(`${getArabicText('date')} ${new Date(invoice.invoice_date).toLocaleDateString('ar-SA')}`, margin, margin + 20, { align: "left" });

  // خط فاصل تحت الرأس
  doc.line(margin, margin + 25, pageWidth - margin, margin + 25);

  // معلومات العميل
  let currentY = margin + 35;
  doc.setFontSize(12);
  doc.text(`${getArabicText('customer')} ${invoice.customers?.name || getArabicText('not_specified')}`, pageWidth - margin, currentY, { align: "right" });
  currentY += 5;

  if (invoice.customers?.phone) {
    doc.text(`${getArabicText('phone')} ${invoice.customers.phone}`, pageWidth - margin, currentY, { align: "right" });
    currentY += 5;
  }

  if (invoice.customers?.email) {
    doc.text(`${getArabicText('email')} ${invoice.customers.email}`, pageWidth - margin, currentY, { align: "right" });
    currentY += 5;
  }
  
  // جدول المنتجات (مطابق للطباعة العربية)
  currentY += 10;

  // جدول المنتجات
  doc.setFontSize(12);

  // رؤوس الجدول
  doc.text(getArabicText('product'), pageWidth - margin, currentY, { align: "right" });
  doc.text(getArabicText('quantity'), pageWidth - margin - 60, currentY, { align: "right" });
  doc.text(getArabicText('price'), pageWidth - margin - 90, currentY, { align: "right" });
  doc.text(getArabicText('total'), pageWidth - margin - 130, currentY, { align: "right" });
  currentY += 5;

  // خط تحت الرؤوس
  doc.line(margin, currentY, pageWidth - margin, currentY);
  currentY += 5;

  // عناصر الفاتورة
  doc.setFontSize(10);
  invoice.invoice_items?.forEach((item) => {
    doc.text(item.products?.name || getArabicText('unknown'), pageWidth - margin, currentY, { align: "right" });
    doc.text(item.quantity.toString(), pageWidth - margin - 60, currentY, { align: "right" });
    doc.text(`${item.unit_price.toFixed(2)} ${getArabicText('currency')}`, pageWidth - margin - 90, currentY, { align: "right" });
    doc.text(`${item.total_price.toFixed(2)} ${getArabicText('currency')}`, pageWidth - margin - 130, currentY, { align: "right" });
    currentY += 5;
  });

  // خط تحت العناصر
  currentY += 5;
  doc.line(margin, currentY, pageWidth - margin, currentY);

  // ملخص المبالغ (مطابق للطباعة العربية)
  let finalY = currentY + 10;

  doc.setFontSize(12);
  doc.text(`${getArabicText('total_amount')} ${formatCurrency(invoice.total_amount)}`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;

  doc.text(`${getArabicText('paid_amount')} ${formatCurrency(invoice.paid_amount)}`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;
  doc.text(`${getArabicText('remaining')} ${formatCurrency(invoice.remaining_amount)}`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;

  doc.text(`${getArabicText('payment_method')} ${
    invoice.payment_method === 'cash' ? getArabicText('cash') :
    invoice.payment_method === 'card' ? getArabicText('card') :
    invoice.payment_method
  }`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;

  doc.text(`${getArabicText('payment_status')} ${
    invoice.payment_status === 'paid' ? getArabicText('paid') :
    invoice.payment_status === 'partial' ? getArabicText('partial') :
    getArabicText('unpaid')
  }`, pageWidth - margin, finalY, { align: "right" });
  finalY += 15;

  // ملاحظات (إن وجدت)
  if (invoice.notes) {
    doc.setFontSize(12);
    doc.text(`${getArabicText('notes')}`, pageWidth - margin, finalY, { align: "right" });
    finalY += 5;
    doc.text(invoice.notes, pageWidth - margin, finalY, { align: "right" });
    finalY += 10;
  }

  // رسالة شكر (مطابقة للطباعة)
  finalY += 20;
  doc.setFontSize(12);
  doc.text(getArabicText('thank_you'), pageWidth / 2, finalY, { align: 'center' });
  
  // تصدير الملف
  return doc;
};

interface InvoiceFromDB {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date?: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string | null;
  notes?: string | null;
  customers?: {
    name: string;
    phone?: string;
    email?: string | null;
  };
  invoice_items?: Array<{
    quantity: number;
    unit_price: number;
    total_price: number;
    products?: {
      name: string;
    };
  }>;
}

export const downloadInvoicePDF = async (invoice: InvoiceFromDB): Promise<boolean> => {
  try {
    // تحويل الكائن إلى النوع المطلوب
    const invoiceData: InvoiceType = {
      id: invoice.id,
      invoice_number: invoice.invoice_number,
      invoice_date: invoice.invoice_date,
      due_date: invoice.due_date,
      total_amount: invoice.total_amount,
      paid_amount: invoice.paid_amount,
      remaining_amount: invoice.remaining_amount,
      payment_status: invoice.payment_status,
      payment_method: invoice.payment_method,
      notes: invoice.notes,
      customers: invoice.customers ? {
        name: invoice.customers.name,
        phone: invoice.customers.phone,
        email: invoice.customers.email
      } : undefined,
      invoice_items: invoice.invoice_items ? invoice.invoice_items.map((item) => ({
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        products: item.products ? {
          name: item.products.name
        } : undefined
      })) : undefined
    };
    
    const doc = await generateInvoicePDF(invoiceData);
    doc.save(`invoice-${invoice.invoice_number}.pdf`);
    return true;
  } catch (error) {
    console.error('Error generating PDF:', error);
    return false;
  }
};
