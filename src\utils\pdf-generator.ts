// filepath: c:\Users\<USER>\Desktop\dz\dz-store-manager-web\src\utils\pdf-generator.ts
import { jsPDF } from "jspdf";
import { formatCurrency } from "./currency";
import html2canvas from "html2canvas";

// تحميل jspdf-autotable
import('jspdf-autotable').then(() => {
  console.log('jspdf-autotable loaded');
}).catch(err => {
  console.error('Failed to load jspdf-autotable:', err);
});

// دالة لتنسيق التاريخ بالفرنسية
const formatDateFrench = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

// دالة لإنشاء PDF من HTML مع دعم العربية
const generatePDFFromHTML = async (invoice: InvoiceType): Promise<jsPDF> => {
  // إنشاء HTML مطابق لصفحة الطباعة
  const htmlContent = `
    <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto;">
      <div style="display: flex; justify-content: space-between; border-bottom: 1px solid #ccc; padding-bottom: 10px; margin-bottom: 20px;">
        <div style="font-size: 24px; font-weight: bold;">دي زد - إدارة المخزون</div>
        <div>
          <div style="font-size: 16px; margin-bottom: 5px;">فاتورة ${invoice.invoice_number}</div>
          <div>تاريخ: ${formatDateFrench(invoice.invoice_date)}</div>
        </div>
      </div>

      <div style="margin: 20px 0;">
        <div style="margin-bottom: 5px;"><strong>العميل:</strong> ${invoice.customers?.name || 'غير محدد'}</div>
        ${invoice.customers?.phone ? `<div style="margin-bottom: 5px;"><strong>رقم الهاتف:</strong> ${invoice.customers.phone}</div>` : ''}
        ${invoice.customers?.email ? `<div style="margin-bottom: 5px;"><strong>البريد الإلكتروني:</strong> ${invoice.customers.email}</div>` : ''}
      </div>

      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="text-align: right; padding: 10px; border-bottom: 1px solid #ddd;">المنتج</th>
            <th style="text-align: right; padding: 10px; border-bottom: 1px solid #ddd;">الكمية</th>
            <th style="text-align: right; padding: 10px; border-bottom: 1px solid #ddd;">السعر</th>
            <th style="text-align: right; padding: 10px; border-bottom: 1px solid #ddd;">المجموع</th>
          </tr>
        </thead>
        <tbody>
          ${invoice.invoice_items?.map(item => `
            <tr>
              <td style="text-align: right; padding: 10px; border-bottom: 1px solid #eee;">${item.products?.name || 'غير معروف'}</td>
              <td style="text-align: right; padding: 10px; border-bottom: 1px solid #eee;">${item.quantity}</td>
              <td style="text-align: right; padding: 10px; border-bottom: 1px solid #eee;">${formatCurrency(item.unit_price)}</td>
              <td style="text-align: right; padding: 10px; border-bottom: 1px solid #eee;">${formatCurrency(item.total_price)}</td>
            </tr>
          `).join('') || ''}
        </tbody>
      </table>

      <div style="margin-top: 20px; text-align: left;">
        <div style="margin-bottom: 5px;"><strong>المجموع:</strong> ${formatCurrency(invoice.total_amount)}</div>
        <div style="margin-bottom: 5px;"><strong>المدفوع:</strong> ${formatCurrency(invoice.paid_amount)}</div>
        <div style="margin-bottom: 5px;"><strong>المتبقي:</strong> ${formatCurrency(invoice.remaining_amount)}</div>
        <div style="margin-bottom: 5px;"><strong>طريقة الدفع:</strong> ${
          invoice.payment_method === 'cash' ? 'نقداً' :
          invoice.payment_method === 'card' ? 'بطاقة ائتمان' :
          invoice.payment_method
        }</div>
        <div style="margin-bottom: 5px;"><strong>حالة الدفع:</strong> ${
          invoice.payment_status === 'paid' ? 'مدفوع' :
          invoice.payment_status === 'partial' ? 'مدفوع جزئياً' :
          'غير مدفوع'
        }</div>
      </div>

      ${invoice.notes ? `<div style="margin-top: 20px;"><strong>ملاحظات:</strong> ${invoice.notes}</div>` : ''}

      <div style="margin-top: 40px; text-align: center;">
        <p>شكراً لك</p>
      </div>
    </div>
  `;

  // إنشاء عنصر HTML مؤقت
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;
  tempDiv.style.position = 'absolute';
  tempDiv.style.left = '-9999px';
  tempDiv.style.top = '-9999px';
  tempDiv.style.width = '800px';
  document.body.appendChild(tempDiv);

  try {
    // تحويل HTML إلى canvas
    const canvas = await html2canvas(tempDiv, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff'
    });

    // إنشاء PDF
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const imgData = canvas.toDataURL('image/png');
    const imgWidth = 210; // A4 width in mm
    const pageHeight = 295; // A4 height in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;

    let position = 0;

    // إضافة الصورة إلى PDF
    doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;

    // إضافة صفحات إضافية إذا لزم الأمر
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      doc.addPage();
      doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    return doc;
  } finally {
    // إزالة العنصر المؤقت
    document.body.removeChild(tempDiv);
  }
};

// إضافة التعريفات للـ TypeScript
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: {
      head?: string[][];
      body: (string | number)[][];
      startY?: number;
      theme?: string;
      headStyles?: object;
      bodyStyles?: object;
      styles?: object;
      columnStyles?: object;
      didDrawPage?: () => void;
    }) => jsPDF;
    lastAutoTable: { finalY: number };
  }
}

interface InvoiceType {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date?: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string | null;
  notes?: string | null;
  customers?: {
    name: string;
    phone?: string;
    email?: string;
  };
  invoice_items?: Array<{
    products?: {
      name: string;
      code?: string;
    };
    quantity: number;
    unit_price: number;
    total_price: number;
  }>;
}

export const generateInvoicePDF = async (invoice: InvoiceType): Promise<jsPDF> => {
  // استخدام الطريقة الجديدة مع HTML و canvas
  return await generatePDFFromHTML(invoice);
};

interface InvoiceFromDB {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date?: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string | null;
  notes?: string | null;
  customers?: {
    name: string;
    phone?: string;
    email?: string | null;
  };
  invoice_items?: Array<{
    quantity: number;
    unit_price: number;
    total_price: number;
    products?: {
      name: string;
    };
  }>;
}

export const downloadInvoicePDF = async (invoice: InvoiceFromDB): Promise<boolean> => {
  try {
    // تحويل الكائن إلى النوع المطلوب
    const invoiceData: InvoiceType = {
      id: invoice.id,
      invoice_number: invoice.invoice_number,
      invoice_date: invoice.invoice_date,
      due_date: invoice.due_date,
      total_amount: invoice.total_amount,
      paid_amount: invoice.paid_amount,
      remaining_amount: invoice.remaining_amount,
      payment_status: invoice.payment_status,
      payment_method: invoice.payment_method,
      notes: invoice.notes,
      customers: invoice.customers ? {
        name: invoice.customers.name,
        phone: invoice.customers.phone,
        email: invoice.customers.email
      } : undefined,
      invoice_items: invoice.invoice_items ? invoice.invoice_items.map((item) => ({
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        products: item.products ? {
          name: item.products.name
        } : undefined
      })) : undefined
    };
    
    const doc = await generateInvoicePDF(invoiceData);
    doc.save(`invoice-${invoice.invoice_number}.pdf`);
    return true;
  } catch (error) {
    console.error('Error generating PDF:', error);
    return false;
  }
};
