// filepath: c:\Users\<USER>\Desktop\dz\dz-store-manager-web\src\utils\pdf-generator.ts
import { jsPDF } from "jspdf";
import { formatCurrency } from "./currency";

// تحميل jspdf-autotable ديناميكياً
let autoTableLoaded = false;

const loadAutoTable = async () => {
  if (!autoTableLoaded) {
    await import('jspdf-autotable');
    autoTableLoaded = true;
  }
};

// إضافة التعريفات للـ TypeScript
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: {
      head?: unknown[][];
      body: unknown[][];
      startY?: number;
      theme?: string;
      headStyles?: Record<string, unknown>;
      bodyStyles?: Record<string, unknown>;
      styles?: Record<string, unknown>;
      columnStyles?: Record<string, Record<string, unknown>>;
      didDrawPage?: () => void;
    }) => jsPDF;
    lastAutoTable: { finalY: number };
  }
}

interface InvoiceType {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date?: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string | null;
  notes?: string | null;
  customers?: {
    name: string;
    phone?: string;
    email?: string;
  };
  invoice_items?: Array<{
    products?: {
      name: string;
      code?: string;
    };
    quantity: number;
    unit_price: number;
    total_price: number;
  }>;
}

export const generateInvoicePDF = async (invoice: InvoiceType): Promise<jsPDF> => {
  // تحميل autoTable أولاً
  await loadAutoTable();

  // إنشاء مستند PDF جديد
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
  });
  // نستخدم الخط الافتراضي
  doc.setFont('Helvetica');

  // الهوامش
  const margin = 10;
  const pageWidth = doc.internal.pageSize.width;
  
  // إضافة العنوان
  doc.setFontSize(24);
  doc.text("DZ - Store Manager", pageWidth - margin, margin + 10, { align: "right" });
  
  // معلومات الفاتورة
  doc.setFontSize(12);
  doc.text(`Invoice #: ${invoice.invoice_number}`, pageWidth - margin, margin + 20, { align: "right" });
  doc.text(`Date: ${new Date(invoice.invoice_date).toLocaleDateString()}`, pageWidth - margin, margin + 25, { align: "right" });
  
  if (invoice.due_date) {
    doc.text(`Due Date: ${new Date(invoice.due_date).toLocaleDateString()}`, pageWidth - margin, margin + 30, { align: "right" });
  }
  
  // معلومات العميل
  doc.setFontSize(14);
  doc.text("Customer Information", pageWidth - margin, margin + 40, { align: "right" });
  doc.setFontSize(12);
  doc.text(`Name: ${invoice.customers?.name || 'Not specified'}`, pageWidth - margin, margin + 45, { align: "right" });
    if (invoice.customers?.phone) {
    doc.text(`Phone: ${invoice.customers.phone}`, pageWidth - margin, margin + 50, { align: "right" });
  }
  
  if (invoice.customers?.email) {
    doc.text(`Email: ${invoice.customers.email}`, pageWidth - margin, margin + 55, { align: "right" });
  }
  
  // جدول المنتجات
  const tableColumn = ["Total", "Price", "Qty", "Product"];
  const tableRows = invoice.invoice_items?.map((item) => [
    formatCurrency(item.total_price),
    formatCurrency(item.unit_price),
    item.quantity,
    item.products?.name || 'Unknown'
  ]) || [];
  
  doc.autoTable({
    head: [tableColumn],
    body: tableRows,
    startY: margin + 65,
    theme: 'grid',
    headStyles: { halign: 'right', fillColor: [41, 128, 185], textColor: [255, 255, 255] },
    bodyStyles: { halign: 'right' },    
    styles: { fontSize: 10 },
    columnStyles: {
      0: { halign: 'left' },
      1: { halign: 'left' },
    },
    didDrawPage: function () {
      // رقم الصفحة
      doc.setFontSize(8);
      doc.text(
        `Page ${doc.internal.pages.length}`,
        pageWidth / 2,
        doc.internal.pageSize.height - 10,
        { align: 'center' }
      );
    }
  });
  // ملخص المبالغ
  let finalY = doc.lastAutoTable.finalY + 10;

  doc.setFontSize(12);
  doc.text(`Total: ${formatCurrency(invoice.total_amount)}`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;

  doc.text(`Paid: ${formatCurrency(invoice.paid_amount)}`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;
  doc.text(`Remaining: ${formatCurrency(invoice.remaining_amount)}`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;
  
  doc.text(`Payment Method: ${
    invoice.payment_method === 'cash' ? 'Cash' : 
    invoice.payment_method === 'card' ? 'Card' : 
    invoice.payment_method
  }`, pageWidth - margin, finalY, { align: "right" });
  finalY += 5;

  doc.text(`Payment Status: ${
    invoice.payment_status === 'paid' ? 'Paid' :
    invoice.payment_status === 'partial' ? 'Partial' :
    'Unpaid'
  }`, pageWidth - margin, finalY, { align: "right" });
  finalY += 15;

  // ملاحظات (إن وجدت)
  if (invoice.notes) {
    doc.setFontSize(12);
    doc.text(`Notes:`, pageWidth - margin, finalY, { align: "right" });
    finalY += 5;
    doc.text(invoice.notes, pageWidth - margin, finalY, { align: "right" });
  }
  // معلومات المتجر في أسفل الصفحة
  const shopInfo = "DZ Store Manager - All Rights Reserved";
  
  doc.setFontSize(10);
  doc.text(shopInfo, pageWidth / 2, doc.internal.pageSize.height - 20, { align: 'center' });
  
  // تصدير الملف
  return doc;
};

interface InvoiceFromDB {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date?: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string | null;
  notes?: string | null;
  customers?: {
    name: string;
    phone?: string;
    email?: string | null;
  };
  invoice_items?: Array<{
    quantity: number;
    unit_price: number;
    total_price: number;
    products?: {
      name: string;
    };
  }>;
}

export const downloadInvoicePDF = async (invoice: InvoiceFromDB): Promise<boolean> => {
  try {
    // تحويل الكائن إلى النوع المطلوب
    const invoiceData: InvoiceType = {
      id: invoice.id,
      invoice_number: invoice.invoice_number,
      invoice_date: invoice.invoice_date,
      due_date: invoice.due_date,
      total_amount: invoice.total_amount,
      paid_amount: invoice.paid_amount,
      remaining_amount: invoice.remaining_amount,
      payment_status: invoice.payment_status,
      payment_method: invoice.payment_method,
      notes: invoice.notes,
      customers: invoice.customers ? {
        name: invoice.customers.name,
        phone: invoice.customers.phone,
        email: invoice.customers.email
      } : undefined,
      invoice_items: invoice.invoice_items ? invoice.invoice_items.map((item) => ({
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        products: item.products ? {
          name: item.products.name
        } : undefined
      })) : undefined
    };
    
    const doc = await generateInvoicePDF(invoiceData);
    doc.save(`invoice-${invoice.invoice_number}.pdf`);
    return true;
  } catch (error) {
    console.error('Error generating PDF:', error);
    return false;
  }
};
