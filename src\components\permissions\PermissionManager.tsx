import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Shield, 
  Users, 
  Settings, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Lock,
  Unlock,
  UserCheck,
  UserX
} from 'lucide-react';
import { UserRole, User } from '@/types';
import { toast } from '@/hooks/use-toast';

interface Permission {
  module: string;
  actions: {
    [key: string]: boolean;
  };
}

interface PermissionManagerProps {
  roles: UserRole[];
  users: User[];
  onCreateRole: (role: Omit<UserRole, 'id' | 'created_at'>) => Promise<void>;
  onUpdateRole: (id: string, role: Partial<UserRole>) => Promise<void>;
  onDeleteRole: (id: string) => Promise<void>;
  onUpdateUser: (id: string, user: Partial<User>) => Promise<void>;
  currentUser?: User;
}

const PermissionManager: React.FC<PermissionManagerProps> = ({
  roles,
  users,
  onCreateRole,
  onUpdateRole,
  onDeleteRole,
  onUpdateUser,
  currentUser
}) => {
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [isCreateRoleOpen, setIsCreateRoleOpen] = useState(false);
  const [isEditRoleOpen, setIsEditRoleOpen] = useState(false);
  const [roleForm, setRoleForm] = useState({
    name: '',
    name_ar: '',
    description: '',
    permissions: {} as Record<string, any>
  });

  // الصلاحيات المتاحة
  const availablePermissions = {
    invoices: {
      name: 'الفواتير',
      actions: {
        create: 'إنشاء',
        read: 'عرض',
        update: 'تعديل',
        delete: 'حذف',
        print: 'طباعة',
        return: 'إرجاع',
        suspend: 'تعليق'
      }
    },
    products: {
      name: 'المنتجات',
      actions: {
        create: 'إنشاء',
        read: 'عرض',
        update: 'تعديل',
        delete: 'حذف',
        manage_stock: 'إدارة المخزون'
      }
    },
    customers: {
      name: 'العملاء',
      actions: {
        create: 'إنشاء',
        read: 'عرض',
        update: 'تعديل',
        delete: 'حذف',
        view_balance: 'عرض الرصيد'
      }
    },
    suppliers: {
      name: 'الموردين',
      actions: {
        create: 'إنشاء',
        read: 'عرض',
        update: 'تعديل',
        delete: 'حذف',
        view_balance: 'عرض الرصيد'
      }
    },
    reports: {
      name: 'التقارير',
      actions: {
        view: 'عرض',
        export: 'تصدير',
        financial: 'التقارير المالية',
        inventory: 'تقارير المخزون'
      }
    },
    settings: {
      name: 'الإعدادات',
      actions: {
        view: 'عرض',
        update: 'تعديل',
        backup: 'النسخ الاحتياطي',
        restore: 'الاستعادة'
      }
    },
    users: {
      name: 'المستخدمين',
      actions: {
        create: 'إنشاء',
        read: 'عرض',
        update: 'تعديل',
        delete: 'حذف',
        manage_roles: 'إدارة الأدوار'
      }
    }
  };

  const resetRoleForm = () => {
    setRoleForm({
      name: '',
      name_ar: '',
      description: '',
      permissions: {}
    });
  };

  const handleCreateRole = async () => {
    if (!roleForm.name || !roleForm.name_ar) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم الدور باللغتين العربية والإنجليزية",
        variant: "destructive"
      });
      return;
    }

    try {
      await onCreateRole({
        name: roleForm.name,
        name_ar: roleForm.name_ar,
        description: roleForm.description,
        permissions: roleForm.permissions,
        is_active: true
      });

      setIsCreateRoleOpen(false);
      resetRoleForm();
      toast({
        title: "تم الإنشاء",
        description: "تم إنشاء الدور بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في إنشاء الدور",
        variant: "destructive"
      });
    }
  };

  const handleUpdateRole = async () => {
    if (!selectedRole) return;

    try {
      await onUpdateRole(selectedRole.id, {
        name: roleForm.name,
        name_ar: roleForm.name_ar,
        description: roleForm.description,
        permissions: roleForm.permissions
      });

      setIsEditRoleOpen(false);
      setSelectedRole(null);
      resetRoleForm();
      toast({
        title: "تم التحديث",
        description: "تم تحديث الدور بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث الدور",
        variant: "destructive"
      });
    }
  };

  const handleDeleteRole = async (role: UserRole) => {
    // التحقق من وجود مستخدمين مرتبطين بهذا الدور
    const usersWithRole = users.filter(user => user.role_id === role.id);
    if (usersWithRole.length > 0) {
      toast({
        title: "لا يمكن الحذف",
        description: `يوجد ${usersWithRole.length} مستخدم مرتبط بهذا الدور`,
        variant: "destructive"
      });
      return;
    }

    if (!confirm(`هل أنت متأكد من حذف الدور "${role.name_ar}"؟`)) {
      return;
    }

    try {
      await onDeleteRole(role.id);
      toast({
        title: "تم الحذف",
        description: "تم حذف الدور بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في حذف الدور",
        variant: "destructive"
      });
    }
  };

  const openEditRole = (role: UserRole) => {
    setSelectedRole(role);
    setRoleForm({
      name: role.name,
      name_ar: role.name_ar,
      description: role.description || '',
      permissions: role.permissions || {}
    });
    setIsEditRoleOpen(true);
  };

  const updatePermission = (module: string, action: string, value: boolean) => {
    setRoleForm(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [module]: {
          ...prev.permissions[module],
          [action]: value
        }
      }
    }));
  };

  const hasPermission = (module: string, action: string): boolean => {
    return roleForm.permissions[module]?.[action] || false;
  };

  const toggleUserStatus = async (user: User) => {
    try {
      await onUpdateUser(user.id, {
        is_active: !user.is_active
      });
      toast({
        title: "تم التحديث",
        description: `تم ${user.is_active ? 'إلغاء تفعيل' : 'تفعيل'} المستخدم`
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث حالة المستخدم",
        variant: "destructive"
      });
    }
  };

  const changeUserRole = async (user: User, newRoleId: string) => {
    try {
      await onUpdateUser(user.id, {
        role_id: newRoleId
      });
      toast({
        title: "تم التحديث",
        description: "تم تغيير دور المستخدم بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تغيير دور المستخدم",
        variant: "destructive"
      });
    }
  };

  const canManagePermissions = () => {
    return currentUser?.role?.permissions?.users?.manage_roles || 
           currentUser?.role?.name === 'admin';
  };

  return (
    <div className="space-y-6 p-6" dir="rtl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            إدارة الصلاحيات والأدوار
          </CardTitle>
        </CardHeader>
      </Card>

      <Tabs defaultValue="roles">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="roles">الأدوار والصلاحيات</TabsTrigger>
          <TabsTrigger value="users">المستخدمين</TabsTrigger>
        </TabsList>

        {/* إدارة الأدوار */}
        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>الأدوار المتاحة ({roles.length})</span>
                {canManagePermissions() && (
                  <Dialog open={isCreateRoleOpen} onOpenChange={setIsCreateRoleOpen}>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="w-4 h-4 ml-2" />
                        إنشاء دور جديد
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>إنشاء دور جديد</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-6">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label>اسم الدور (بالإنجليزية)</Label>
                            <Input
                              value={roleForm.name}
                              onChange={(e) => setRoleForm(prev => ({ ...prev, name: e.target.value }))}
                              placeholder="role_name"
                            />
                          </div>
                          <div>
                            <Label>اسم الدور (بالعربية)</Label>
                            <Input
                              value={roleForm.name_ar}
                              onChange={(e) => setRoleForm(prev => ({ ...prev, name_ar: e.target.value }))}
                              placeholder="اسم الدور"
                            />
                          </div>
                        </div>

                        <div>
                          <Label>الوصف</Label>
                          <Textarea
                            value={roleForm.description}
                            onChange={(e) => setRoleForm(prev => ({ ...prev, description: e.target.value }))}
                            placeholder="وصف الدور والصلاحيات..."
                            rows={3}
                          />
                        </div>

                        {/* الصلاحيات */}
                        <div className="space-y-4">
                          <h3 className="text-lg font-semibold">الصلاحيات</h3>
                          <div className="space-y-4">
                            {Object.entries(availablePermissions).map(([module, moduleData]) => (
                              <Card key={module}>
                                <CardHeader className="pb-3">
                                  <CardTitle className="text-base">{moduleData.name}</CardTitle>
                                </CardHeader>
                                <CardContent>
                                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                                    {Object.entries(moduleData.actions).map(([action, actionName]) => (
                                      <div key={action} className="flex items-center space-x-2 space-x-reverse">
                                        <Switch
                                          checked={hasPermission(module, action)}
                                          onCheckedChange={(checked) => updatePermission(module, action, checked)}
                                        />
                                        <Label className="text-sm">{actionName}</Label>
                                      </div>
                                    ))}
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        </div>

                        <div className="flex gap-2">
                          <Button onClick={handleCreateRole} className="flex-1">
                            إنشاء الدور
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setIsCreateRoleOpen(false);
                              resetRoleForm();
                            }}
                            className="flex-1"
                          >
                            إلغاء
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {roles.map((role) => (
                  <Card key={role.id} className="border-l-4 border-l-blue-400">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-4">
                            <div>
                              <div className="font-medium text-lg">{role.name_ar}</div>
                              <div className="text-sm text-gray-500">{role.name}</div>
                              {role.description && (
                                <div className="text-sm text-gray-600 mt-1">{role.description}</div>
                              )}
                            </div>
                            <Badge variant={role.is_active ? 'default' : 'secondary'}>
                              {role.is_active ? 'نشط' : 'غير نشط'}
                            </Badge>
                          </div>

                          <div className="mt-3">
                            <div className="text-sm text-gray-600 mb-2">الصلاحيات:</div>
                            <div className="flex flex-wrap gap-2">
                              {Object.entries(role.permissions || {}).map(([module, permissions]) => {
                                const moduleData = availablePermissions[module as keyof typeof availablePermissions];
                                if (!moduleData) return null;

                                const activeActions = Object.entries(permissions as Record<string, boolean>)
                                  .filter(([_, value]) => value)
                                  .map(([action]) => moduleData.actions[action])
                                  .filter(Boolean);

                                if (activeActions.length === 0) return null;

                                return (
                                  <Badge key={module} variant="outline" className="text-xs">
                                    {moduleData.name}: {activeActions.join(', ')}
                                  </Badge>
                                );
                              })}
                            </div>
                          </div>
                        </div>

                        {canManagePermissions() && (
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openEditRole(role)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            
                            {role.name !== 'admin' && (
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleDeleteRole(role)}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* إدارة المستخدمين */}
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>المستخدمين ({users.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {users.map((user) => (
                  <Card key={user.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-2">
                            {user.is_active ? (
                              <UserCheck className="w-5 h-5 text-green-500" />
                            ) : (
                              <UserX className="w-5 h-5 text-red-500" />
                            )}
                          </div>
                          
                          <div>
                            <div className="font-medium">{user.full_name}</div>
                            <div className="text-sm text-gray-500">@{user.username}</div>
                            {user.email && (
                              <div className="text-sm text-gray-500">{user.email}</div>
                            )}
                          </div>

                          <Badge variant={user.is_active ? 'default' : 'secondary'}>
                            {user.is_active ? 'نشط' : 'معطل'}
                          </Badge>

                          <Badge variant="outline">
                            {user.role?.name_ar || 'بدون دور'}
                          </Badge>
                        </div>

                        {canManagePermissions() && currentUser?.id !== user.id && (
                          <div className="flex gap-2">
                            <select
                              className="p-2 border rounded text-sm"
                              value={user.role_id || ''}
                              onChange={(e) => changeUserRole(user, e.target.value)}
                            >
                              <option value="">بدون دور</option>
                              {roles.map(role => (
                                <option key={role.id} value={role.id}>
                                  {role.name_ar}
                                </option>
                              ))}
                            </select>

                            <Button
                              variant={user.is_active ? "destructive" : "default"}
                              size="sm"
                              onClick={() => toggleUserStatus(user)}
                            >
                              {user.is_active ? (
                                <>
                                  <Lock className="w-4 h-4 ml-2" />
                                  تعطيل
                                </>
                              ) : (
                                <>
                                  <Unlock className="w-4 h-4 ml-2" />
                                  تفعيل
                                </>
                              )}
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* نافذة تعديل الدور */}
      <Dialog open={isEditRoleOpen} onOpenChange={setIsEditRoleOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>تعديل الدور: {selectedRole?.name_ar}</DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>اسم الدور (بالإنجليزية)</Label>
                <Input
                  value={roleForm.name}
                  onChange={(e) => setRoleForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="role_name"
                />
              </div>
              <div>
                <Label>اسم الدور (بالعربية)</Label>
                <Input
                  value={roleForm.name_ar}
                  onChange={(e) => setRoleForm(prev => ({ ...prev, name_ar: e.target.value }))}
                  placeholder="اسم الدور"
                />
              </div>
            </div>

            <div>
              <Label>الوصف</Label>
              <Textarea
                value={roleForm.description}
                onChange={(e) => setRoleForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="وصف الدور والصلاحيات..."
                rows={3}
              />
            </div>

            {/* الصلاحيات */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">الصلاحيات</h3>
              <div className="space-y-4">
                {Object.entries(availablePermissions).map(([module, moduleData]) => (
                  <Card key={module}>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">{moduleData.name}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {Object.entries(moduleData.actions).map(([action, actionName]) => (
                          <div key={action} className="flex items-center space-x-2 space-x-reverse">
                            <Switch
                              checked={hasPermission(module, action)}
                              onCheckedChange={(checked) => updatePermission(module, action, checked)}
                            />
                            <Label className="text-sm">{actionName}</Label>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              <Button onClick={handleUpdateRole} className="flex-1">
                حفظ التغييرات
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditRoleOpen(false);
                  setSelectedRole(null);
                  resetRoleForm();
                }}
                className="flex-1"
              >
                إلغاء
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PermissionManager;
