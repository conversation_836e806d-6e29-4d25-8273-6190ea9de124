import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Plus, Minus, Save, Print, ShoppingCart } from 'lucide-react';
import { Invoice, InvoiceItem, Supplier, Product, PaymentMethod, PaymentStatus } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { toast } from '@/hooks/use-toast';

interface PurchaseInvoiceProps {
  invoice?: Invoice;
  onSave: (invoice: Partial<Invoice>) => Promise<void>;
  suppliers: Supplier[];
  products: Product[];
}

const PurchaseInvoice: React.FC<PurchaseInvoiceProps> = ({
  invoice,
  onSave,
  suppliers,
  products
}) => {
  const [formData, setFormData] = useState<Partial<Invoice>>({
    type: 'purchase',
    supplier_id: '',
    items: [],
    subtotal_amount: 0,
    discount_amount: 0,
    discount_percent: 0,
    tax_amount: 0,
    tax_rate: 0,
    total_amount: 0,
    paid_amount: 0,
    remaining_amount: 0,
    cash_amount: 0,
    card_amount: 0,
    credit_amount: 0,
    payment_method: 'credit',
    payment_status: 'unpaid',
    currency: 'DZD',
    exchange_rate: 1.0,
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: '',
    notes: '',
    is_suspended: false
  });

  const [currentItem, setCurrentItem] = useState<Partial<InvoiceItem>>({
    product_id: '',
    quantity: 1,
    unit_price: 0,
    discount_percent: 0,
    tax_percent: 0
  });

  useEffect(() => {
    if (invoice) {
      setFormData(invoice);
    }
  }, [invoice]);

  useEffect(() => {
    calculateTotals();
  }, [formData.items, formData.discount_percent, formData.tax_rate]);

  const calculateTotals = () => {
    const items = formData.items || [];
    const subtotal = items.reduce((sum, item) => sum + (item.total_price || 0), 0);
    const discountAmount = formData.discount_percent ? (subtotal * formData.discount_percent / 100) : (formData.discount_amount || 0);
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = formData.tax_rate ? (taxableAmount * formData.tax_rate / 100) : 0;
    const total = taxableAmount + taxAmount;
    const remaining = total - (formData.paid_amount || 0);

    setFormData(prev => ({
      ...prev,
      subtotal_amount: subtotal,
      discount_amount: discountAmount,
      tax_amount: taxAmount,
      total_amount: total,
      remaining_amount: remaining
    }));
  };

  const addItem = () => {
    if (!currentItem.product_id || !currentItem.quantity || !currentItem.unit_price) {
      toast({
        title: "خطأ",
        description: "يرجى اختيار المنتج وإدخال الكمية والسعر",
        variant: "destructive"
      });
      return;
    }

    const product = products.find(p => p.id === currentItem.product_id);
    if (!product) return;

    const quantity = currentItem.quantity || 1;
    const unitPrice = currentItem.unit_price || 0;
    const discountAmount = (unitPrice * quantity * (currentItem.discount_percent || 0)) / 100;
    const taxableAmount = (unitPrice * quantity) - discountAmount;
    const taxAmount = (taxableAmount * (currentItem.tax_percent || 0)) / 100;
    const totalPrice = taxableAmount + taxAmount;

    const newItem: InvoiceItem = {
      product_id: currentItem.product_id,
      product_name: product.name,
      quantity,
      unit_price: unitPrice,
      original_price: product.purchasePrice,
      discount_amount: discountAmount,
      discount_percent: currentItem.discount_percent || 0,
      tax_amount: taxAmount,
      tax_percent: currentItem.tax_percent || 0,
      total_price: totalPrice,
      notes: currentItem.notes || '',
      product
    };

    setFormData(prev => ({
      ...prev,
      items: [...(prev.items || []), newItem]
    }));

    // إعادة تعيين العنصر الحالي
    setCurrentItem({
      product_id: '',
      quantity: 1,
      unit_price: 0,
      discount_percent: 0,
      tax_percent: 0
    });
  };

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items?.filter((_, i) => i !== index) || []
    }));
  };

  const updatePaymentAmounts = () => {
    const total = formData.total_amount || 0;
    const cash = formData.cash_amount || 0;
    const card = formData.card_amount || 0;
    const credit = formData.credit_amount || 0;
    const totalPaid = cash + card + credit;

    let paymentMethod: PaymentMethod = 'credit';
    let paymentStatus: PaymentStatus = 'unpaid';

    if (totalPaid >= total) {
      paymentStatus = 'paid';
    } else if (totalPaid > 0) {
      paymentStatus = 'partial';
    }

    if (cash > 0 && card > 0) {
      paymentMethod = 'mixed';
    } else if (card > 0) {
      paymentMethod = 'card';
    } else if (cash > 0) {
      paymentMethod = 'cash';
    }

    setFormData(prev => ({
      ...prev,
      paid_amount: totalPaid,
      remaining_amount: total - totalPaid,
      payment_method: paymentMethod,
      payment_status: paymentStatus
    }));
  };

  const handleSave = async () => {
    if (!formData.supplier_id) {
      toast({
        title: "خطأ",
        description: "يرجى اختيار المورد",
        variant: "destructive"
      });
      return;
    }

    if (!formData.items || formData.items.length === 0) {
      toast({
        title: "خطأ",
        description: "يرجى إضافة منتج واحد على الأقل",
        variant: "destructive"
      });
      return;
    }

    try {
      await onSave(formData);
      toast({
        title: "تم الحفظ",
        description: "تم حفظ فاتورة الشراء بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في حفظ فاتورة الشراء",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6 p-6" dir="rtl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <ShoppingCart className="w-5 h-5 ml-2" />
              فاتورة شراء جديدة
            </span>
            <Badge variant="outline">
              {formData.invoice_number || 'جديدة'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* معلومات المورد */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="supplier">المورد *</Label>
              <Select
                value={formData.supplier_id}
                onValueChange={(value) => setFormData(prev => ({ ...prev, supplier_id: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر المورد" />
                </SelectTrigger>
                <SelectContent>
                  {suppliers.map(supplier => (
                    <SelectItem key={supplier.id} value={supplier.id}>
                      {supplier.name} - {supplier.phone}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="date">تاريخ الفاتورة</Label>
              <Input
                type="date"
                value={formData.invoice_date}
                onChange={(e) => setFormData(prev => ({ ...prev, invoice_date: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="due_date">تاريخ الاستحقاق</Label>
              <Input
                type="date"
                value={formData.due_date}
                onChange={(e) => setFormData(prev => ({ ...prev, due_date: e.target.value }))}
              />
            </div>
          </div>

          <Separator />

          {/* إضافة المنتجات */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">إضافة منتج</h3>
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              <div>
                <Label>المنتج *</Label>
                <Select
                  value={currentItem.product_id}
                  onValueChange={(value) => {
                    const product = products.find(p => p.id === value);
                    setCurrentItem(prev => ({
                      ...prev,
                      product_id: value,
                      unit_price: product?.purchasePrice || 0
                    }));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر المنتج" />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map(product => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>الكمية *</Label>
                <Input
                  type="number"
                  min="1"
                  value={currentItem.quantity}
                  onChange={(e) => setCurrentItem(prev => ({ ...prev, quantity: parseInt(e.target.value) || 1 }))}
                />
              </div>
              <div>
                <Label>سعر الشراء *</Label>
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  value={currentItem.unit_price}
                  onChange={(e) => setCurrentItem(prev => ({ ...prev, unit_price: parseFloat(e.target.value) || 0 }))}
                />
              </div>
              <div>
                <Label>خصم %</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={currentItem.discount_percent}
                  onChange={(e) => setCurrentItem(prev => ({ ...prev, discount_percent: parseFloat(e.target.value) || 0 }))}
                />
              </div>
              <div>
                <Label>ضريبة %</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={currentItem.tax_percent}
                  onChange={(e) => setCurrentItem(prev => ({ ...prev, tax_percent: parseFloat(e.target.value) || 0 }))}
                />
              </div>
              <div className="flex items-end">
                <Button onClick={addItem} className="w-full">
                  <Plus className="w-4 h-4 ml-2" />
                  إضافة
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول العناصر */}
      {formData.items && formData.items.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>عناصر الفاتورة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-2">المنتج</th>
                    <th className="text-right p-2">الكمية</th>
                    <th className="text-right p-2">سعر الشراء</th>
                    <th className="text-right p-2">الخصم</th>
                    <th className="text-right p-2">الضريبة</th>
                    <th className="text-right p-2">المجموع</th>
                    <th className="text-right p-2">إجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.items.map((item, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2">{item.product_name}</td>
                      <td className="p-2">{item.quantity}</td>
                      <td className="p-2">{formatCurrency(item.unit_price)}</td>
                      <td className="p-2">
                        {item.discount_percent > 0 ? `${item.discount_percent}%` : '-'}
                      </td>
                      <td className="p-2">
                        {item.tax_percent > 0 ? `${item.tax_percent}%` : '-'}
                      </td>
                      <td className="p-2 font-semibold">{formatCurrency(item.total_price)}</td>
                      <td className="p-2">
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => removeItem(index)}
                        >
                          <Minus className="w-4 h-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* المجاميع والدفع */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* المجاميع */}
        <Card>
          <CardHeader>
            <CardTitle>ملخص الفاتورة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span>المجموع الفرعي:</span>
              <span className="font-semibold">{formatCurrency(formData.subtotal_amount || 0)}</span>
            </div>

            <div className="flex justify-between items-center">
              <span>خصم عام (%):</span>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={formData.discount_percent}
                  onChange={(e) => setFormData(prev => ({ ...prev, discount_percent: parseFloat(e.target.value) || 0 }))}
                  className="w-20"
                />
                <span className="text-sm">%</span>
              </div>
            </div>

            <div className="flex justify-between">
              <span>مبلغ الخصم:</span>
              <span className="text-red-600">-{formatCurrency(formData.discount_amount || 0)}</span>
            </div>

            <div className="flex justify-between items-center">
              <span>ضريبة (%):</span>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={formData.tax_rate}
                  onChange={(e) => setFormData(prev => ({ ...prev, tax_rate: parseFloat(e.target.value) || 0 }))}
                  className="w-20"
                />
                <span className="text-sm">%</span>
              </div>
            </div>

            <div className="flex justify-between">
              <span>مبلغ الضريبة:</span>
              <span className="text-green-600">+{formatCurrency(formData.tax_amount || 0)}</span>
            </div>

            <Separator />

            <div className="flex justify-between text-lg font-bold">
              <span>المجموع الكلي:</span>
              <span>{formatCurrency(formData.total_amount || 0)}</span>
            </div>
          </CardContent>
        </Card>

        {/* الدفع */}
        <Card>
          <CardHeader>
            <CardTitle>تفاصيل الدفع</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div>
                <Label>نقداً</Label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.cash_amount}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, cash_amount: parseFloat(e.target.value) || 0 }));
                    setTimeout(updatePaymentAmounts, 100);
                  }}
                />
              </div>

              <div>
                <Label>بطاقة</Label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.card_amount}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, card_amount: parseFloat(e.target.value) || 0 }));
                    setTimeout(updatePaymentAmounts, 100);
                  }}
                />
              </div>

              <div>
                <Label>آجل (دين على المورد)</Label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.credit_amount}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, credit_amount: parseFloat(e.target.value) || 0 }));
                    setTimeout(updatePaymentAmounts, 100);
                  }}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <div className="flex justify-between">
                <span>إجمالي المدفوع:</span>
                <span className="font-semibold text-green-600">
                  {formatCurrency(formData.paid_amount || 0)}
                </span>
              </div>

              <div className="flex justify-between">
                <span>المتبقي:</span>
                <span className={`font-semibold ${(formData.remaining_amount || 0) > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {formatCurrency(formData.remaining_amount || 0)}
                </span>
              </div>

              <div className="flex justify-between">
                <span>حالة الدفع:</span>
                <Badge variant={
                  formData.payment_status === 'paid' ? 'default' :
                  formData.payment_status === 'partial' ? 'secondary' : 'destructive'
                }>
                  {formData.payment_status === 'paid' ? 'مدفوع' :
                   formData.payment_status === 'partial' ? 'مدفوع جزئياً' : 'غير مدفوع'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* الملاحظات */}
      <Card>
        <CardHeader>
          <CardTitle>ملاحظات</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="أدخل أي ملاحظات إضافية حول فاتورة الشراء..."
            value={formData.notes}
            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            rows={3}
          />
        </CardContent>
      </Card>

      {/* أزرار الإجراءات */}
      <div className="flex justify-end space-x-4 space-x-reverse">
        <Button variant="outline">
          <Print className="w-4 h-4 ml-2" />
          طباعة
        </Button>

        <Button onClick={handleSave}>
          <Save className="w-4 h-4 ml-2" />
          حفظ فاتورة الشراء
        </Button>
      </div>
    </div>
  );
};

export default PurchaseInvoice;
