# دليل حل المشاكل - نظام إدارة الفواتير المتقدم

## 🔧 المشاكل الشائعة وحلولها

### 1. 🖥️ شاشة بيضاء أو أخطاء JavaScript

#### المشكلة:
```
Uncaught SyntaxError: The requested module does not provide an export named 'Print'
```

#### الحل:
✅ **تم إصلاح هذه المشكلة!** 
- تم استبدال `Print` بـ `Printer`
- تم استبدال `Suspend` بـ `Pause`
- جميع الأيقونات الآن متوافقة مع `lucide-react`

#### إذا واجهت مشاكل مشابهة:
1. افتح وحدة التحكم في المتصفح (F12)
2. ابحث عن أخطاء الاستيراد
3. تأكد من أن جميع الأيقونات موجودة في `lucide-react`

### 2. 🌐 لا يمكن الوصول للتطبيق

#### المشكلة:
- التطبيق لا يفتح على المنفذ المتوقع

#### الحل:
1. **تحقق من المنفذ الصحيح:**
   ```bash
   npm run dev
   ```
   ابحث عن الرسالة: `Local: http://localhost:XXXX/`

2. **المنافذ الشائعة:**
   - `http://localhost:8081/` (الحالي)
   - `http://localhost:8080/`
   - `http://localhost:3000/`

3. **إذا كان المنفذ مشغولاً:**
   ```bash
   # إيقاف العمليات على المنفذ (Windows)
   netstat -ano | findstr :8080
   taskkill /PID [رقم_العملية] /F
   ```

### 3. 📊 الميزات الجديدة لا تظهر

#### المشكلة:
- لا تظهر الصفحات الجديدة في الشريط الجانبي

#### الحل:
1. **تحديث الصفحة:**
   - اضغط `Ctrl + F5` (تحديث قسري)
   - أو `Ctrl + Shift + R`

2. **مسح ذاكرة التخزين المؤقت:**
   - افتح أدوات المطور (F12)
   - انقر بزر الماوس الأيمن على زر التحديث
   - اختر "Empty Cache and Hard Reload"

3. **التحقق من الروابط الجديدة:**
   - لوحة التحكم المتقدمة: `/dashboard`
   - نظام الفواتير المتقدم: `/invoices-new`

### 4. 🗄️ مشاكل قاعدة البيانات

#### المشكلة:
- أخطاء في تحميل البيانات
- "Failed to fetch" errors

#### الحل:
1. **تحقق من اتصال Supabase:**
   - تأكد من صحة متغيرات البيئة
   - تحقق من ملف `.env.local`

2. **إعادة تشغيل الخادم:**
   ```bash
   # إيقاف الخادم (Ctrl+C)
   # ثم إعادة التشغيل
   npm run dev
   ```

3. **تحقق من الجداول:**
   - تأكد من وجود الجداول الجديدة في Supabase
   - راجع ملف `database-schema.sql`

### 5. 🎨 مشاكل التصميم

#### المشكلة:
- التصميم لا يظهر بشكل صحيح
- الألوان أو الخطوط غير صحيحة

#### الحل:
1. **تحقق من Tailwind CSS:**
   ```bash
   npm install -D tailwindcss
   ```

2. **إعادة بناء الأنماط:**
   ```bash
   npm run build
   npm run dev
   ```

### 6. 📱 مشاكل الاستجابة (Responsive)

#### المشكلة:
- التطبيق لا يعمل بشكل جيد على الهاتف

#### الحل:
1. **تحقق من viewport:**
   - تأكد من وجود meta tag للـ viewport
   - استخدم أدوات المطور لمحاكاة الأجهزة المحمولة

2. **اختبار على أحجام مختلفة:**
   - F12 → Device Toolbar
   - اختبر على أحجام مختلفة

### 7. 🔍 مشاكل البحث والمرشحات

#### المشكلة:
- البحث الذكي لا يعمل
- المرشحات لا تظهر نتائج

#### الحل:
1. **تحقق من البيانات:**
   - تأكد من وجود بيانات في قاعدة البيانات
   - تحقق من صحة العلاقات بين الجداول

2. **اختبار البحث:**
   - جرب البحث بكلمات بسيطة
   - تأكد من وجود المنتجات والعملاء

### 8. 🖨️ مشاكل الطباعة والتصدير

#### المشكلة:
- لا يمكن طباعة الفواتير
- التصدير لا يعمل

#### الحل:
1. **تحقق من مكتبات PDF:**
   ```bash
   npm install jspdf html2canvas
   ```

2. **اختبار الطباعة:**
   - استخدم Ctrl+P للطباعة العادية
   - تحقق من إعدادات المتصفح

### 9. 📊 مشاكل الرسوم البيانية

#### المشكلة:
- الرسوم البيانية لا تظهر
- أخطاء في Recharts

#### الحل:
1. **تحقق من البيانات:**
   - تأكد من وجود بيانات للرسوم البيانية
   - تحقق من تنسيق البيانات

2. **إعادة تثبيت Recharts:**
   ```bash
   npm install recharts
   ```

### 10. 🔐 مشاكل الصلاحيات

#### المشكلة:
- رسائل "غير مصرح بالوصول"
- المكونات لا تظهر

#### الحل:
1. **تحقق من دور المستخدم:**
   - تأكد من تعيين دور للمستخدم
   - راجع إعدادات الصلاحيات

2. **اختبار بدور المدير:**
   - استخدم حساب المدير للاختبار
   - تحقق من عمل جميع الميزات

## 🆘 طلب المساعدة

### إذا لم تحل المشكلة:

1. **جمع المعلومات:**
   - نسخ رسالة الخطأ كاملة
   - تحديد الخطوات التي أدت للمشكلة
   - أخذ لقطة شاشة إذا أمكن

2. **فحص وحدة التحكم:**
   - افتح F12
   - انتقل لتبويب Console
   - انسخ أي أخطاء ظاهرة

3. **فحص Network:**
   - في أدوات المطور، انتقل لتبويب Network
   - تحقق من وجود طلبات فاشلة (أحمر)

4. **معلومات النظام:**
   - نظام التشغيل
   - نوع وإصدار المتصفح
   - إصدار Node.js

## ✅ نصائح للوقاية

1. **تحديث منتظم:**
   ```bash
   npm update
   ```

2. **نسخ احتياطية:**
   - احتفظ بنسخة من قاعدة البيانات
   - احفظ ملفات التكوين

3. **اختبار دوري:**
   - اختبر الميزات الجديدة بعد كل تحديث
   - تأكد من عمل جميع الوظائف

4. **مراقبة الأداء:**
   - راقب سرعة التطبيق
   - تحقق من استهلاك الذاكرة

---

**💡 تذكر: معظم المشاكل يمكن حلها بإعادة تشغيل الخادم وتحديث المتصفح!**
