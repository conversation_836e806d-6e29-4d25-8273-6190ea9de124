import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Pause, 
  Play, 
  Trash2, 
  Search, 
  Clock, 
  User, 
  Calendar,
  FileText,
  Eye
} from 'lucide-react';
import { SuspendedInvoice, Customer, Supplier } from '@/types';
import { formatCurrency } from '@/utils/currency';
import { toast } from '@/hooks/use-toast';

interface SuspendedInvoicesProps {
  suspendedInvoices: SuspendedInvoice[];
  customers: Customer[];
  suppliers: Supplier[];
  onRestoreInvoice: (suspendedInvoice: SuspendedInvoice) => Promise<void>;
  onDeleteSuspendedInvoice: (id: string) => Promise<void>;
  onViewInvoiceData: (invoiceData: any) => void;
}

const SuspendedInvoices: React.FC<SuspendedInvoicesProps> = ({
  suspendedInvoices,
  customers,
  suppliers,
  onRestoreInvoice,
  onDeleteSuspendedInvoice,
  onViewInvoiceData
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedInvoice, setSelectedInvoice] = useState<SuspendedInvoice | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  // تصفية الفواتير المعلقة
  const filteredInvoices = suspendedInvoices.filter(invoice => {
    if (!searchTerm) return true;
    
    const customer = customers.find(c => c.id === invoice.customer_id);
    const supplier = suppliers.find(s => s.id === invoice.supplier_id);
    const entityName = customer?.name || supplier?.name || '';
    
    return (
      entityName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (invoice.notes && invoice.notes.toLowerCase().includes(searchTerm.toLowerCase())) ||
      invoice.id.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const handleRestoreInvoice = async (suspendedInvoice: SuspendedInvoice) => {
    try {
      await onRestoreInvoice(suspendedInvoice);
      toast({
        title: "تم الاستعادة",
        description: "تم استعادة الفاتورة المعلقة بنجاح"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في استعادة الفاتورة المعلقة",
        variant: "destructive"
      });
    }
  };

  const handleDeleteSuspendedInvoice = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه الفاتورة المعلقة نهائياً؟')) {
      return;
    }

    try {
      await onDeleteSuspendedInvoice(id);
      toast({
        title: "تم الحذف",
        description: "تم حذف الفاتورة المعلقة نهائياً"
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في حذف الفاتورة المعلقة",
        variant: "destructive"
      });
    }
  };

  const viewInvoiceDetails = (suspendedInvoice: SuspendedInvoice) => {
    setSelectedInvoice(suspendedInvoice);
    setIsViewDialogOpen(true);
  };

  const getEntityInfo = (suspendedInvoice: SuspendedInvoice) => {
    if (suspendedInvoice.customer_id) {
      const customer = customers.find(c => c.id === suspendedInvoice.customer_id);
      return {
        name: customer?.name || 'عميل غير معروف',
        type: 'عميل',
        phone: customer?.phone
      };
    } else if (suspendedInvoice.supplier_id) {
      const supplier = suppliers.find(s => s.id === suspendedInvoice.supplier_id);
      return {
        name: supplier?.name || 'مورد غير معروف',
        type: 'مورد',
        phone: supplier?.phone
      };
    }
    return {
      name: 'غير محدد',
      type: 'غير معروف',
      phone: undefined
    };
  };

  const getInvoiceTotal = (invoiceData: any) => {
    return invoiceData?.total_amount || 0;
  };

  const getInvoiceType = (invoiceData: any) => {
    const type = invoiceData?.type;
    switch (type) {
      case 'sales':
        return 'بيع';
      case 'purchase':
        return 'شراء';
      case 'sales_return':
        return 'إرجاع بيع';
      case 'purchase_return':
        return 'إرجاع شراء';
      default:
        return 'غير محدد';
    }
  };

  const getItemsCount = (invoiceData: any) => {
    return invoiceData?.items?.length || 0;
  };

  return (
    <div className="space-y-6 p-6" dir="rtl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Pause className="w-5 h-5" />
              الفواتير المعلقة ({filteredInvoices.length})
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* البحث */}
          <div className="flex gap-4">
            <div className="flex-1">
              <Label>البحث في الفواتير المعلقة</Label>
              <div className="relative">
                <Search className="absolute right-3 top-3 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="البحث بالعميل، المورد، أو الملاحظات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
          </div>

          {/* قائمة الفواتير المعلقة */}
          {filteredInvoices.length > 0 ? (
            <div className="space-y-4">
              {filteredInvoices.map((suspendedInvoice) => {
                const entityInfo = getEntityInfo(suspendedInvoice);
                const invoiceTotal = getInvoiceTotal(suspendedInvoice.invoice_data);
                const invoiceType = getInvoiceType(suspendedInvoice.invoice_data);
                const itemsCount = getItemsCount(suspendedInvoice.invoice_data);

                return (
                  <Card key={suspendedInvoice.id} className="border-l-4 border-l-yellow-400">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-4">
                            <Badge variant="outline" className="flex items-center gap-1">
                              <FileText className="w-3 h-3" />
                              {invoiceType}
                            </Badge>
                            
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <User className="w-4 h-4" />
                              <span>{entityInfo.type}: {entityInfo.name}</span>
                              {entityInfo.phone && (
                                <span className="text-gray-400">({entityInfo.phone})</span>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center gap-6 text-sm">
                            <div className="flex items-center gap-2">
                              <Calendar className="w-4 h-4 text-gray-400" />
                              <span>معلقة منذ: {new Date(suspendedInvoice.suspended_at).toLocaleDateString('ar-SA')}</span>
                            </div>
                            
                            <div className="font-semibold text-green-600">
                              المجموع: {formatCurrency(invoiceTotal)}
                            </div>
                            
                            <div className="text-gray-600">
                              العناصر: {itemsCount}
                            </div>
                          </div>

                          {suspendedInvoice.notes && (
                            <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                              <strong>ملاحظات:</strong> {suspendedInvoice.notes}
                            </div>
                          )}
                        </div>

                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => viewInvoiceDetails(suspendedInvoice)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => handleRestoreInvoice(suspendedInvoice)}
                          >
                            <Play className="w-4 h-4 ml-2" />
                            استعادة
                          </Button>
                          
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteSuspendedInvoice(suspendedInvoice.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <Clock className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-600 mb-2">
                لا توجد فواتير معلقة
              </h3>
              <p className="text-gray-500">
                {searchTerm ? 'لا توجد فواتير معلقة تطابق البحث' : 'جميع الفواتير مكتملة'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* نافذة عرض تفاصيل الفاتورة المعلقة */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>تفاصيل الفاتورة المعلقة</DialogTitle>
          </DialogHeader>
          {selectedInvoice && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>نوع الفاتورة</Label>
                  <div className="p-2 bg-gray-50 rounded">
                    {getInvoiceType(selectedInvoice.invoice_data)}
                  </div>
                </div>
                
                <div>
                  <Label>تاريخ التعليق</Label>
                  <div className="p-2 bg-gray-50 rounded">
                    {new Date(selectedInvoice.suspended_at).toLocaleDateString('ar-SA')} - 
                    {new Date(selectedInvoice.suspended_at).toLocaleTimeString('ar-SA')}
                  </div>
                </div>
              </div>

              <div>
                <Label>العميل/المورد</Label>
                <div className="p-2 bg-gray-50 rounded">
                  {getEntityInfo(selectedInvoice).name}
                </div>
              </div>

              {selectedInvoice.notes && (
                <div>
                  <Label>ملاحظات التعليق</Label>
                  <div className="p-2 bg-gray-50 rounded">
                    {selectedInvoice.notes}
                  </div>
                </div>
              )}

              <div>
                <Label>عناصر الفاتورة</Label>
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="text-right p-2">المنتج</th>
                        <th className="text-right p-2">الكمية</th>
                        <th className="text-right p-2">السعر</th>
                        <th className="text-right p-2">المجموع</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedInvoice.invoice_data?.items?.map((item: any, index: number) => (
                        <tr key={index} className="border-t">
                          <td className="p-2">{item.product_name}</td>
                          <td className="p-2">{item.quantity}</td>
                          <td className="p-2">{formatCurrency(item.unit_price)}</td>
                          <td className="p-2 font-semibold">{formatCurrency(item.total_price)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex justify-between text-lg font-bold">
                  <span>إجمالي الفاتورة:</span>
                  <span className="text-blue-600">
                    {formatCurrency(getInvoiceTotal(selectedInvoice.invoice_data))}
                  </span>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={() => {
                    handleRestoreInvoice(selectedInvoice);
                    setIsViewDialogOpen(false);
                  }}
                  className="flex-1"
                >
                  <Play className="w-4 h-4 ml-2" />
                  استعادة الفاتورة
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => setIsViewDialogOpen(false)}
                  className="flex-1"
                >
                  إغلاق
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SuspendedInvoices;
