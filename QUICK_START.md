# دليل البدء السريع - الميزات الجديدة

## 🚀 كيفية الوصول للميزات الجديدة:

### 1. تشغيل التطبيق:
```bash
npm run dev
```

### 2. فتح المتصفح والانتقال إلى:
```
http://localhost:8081
```
(ملاحظة: قد يتغير المنفذ حسب التوفر)

## 📋 الميزات الجديدة المتاحة:

### 🎯 1. لوحة التحكم المتقدمة
**الرابط**: `http://localhost:8081/dashboard`

**الميزات**:
- إحصائيات مفصلة مع رسوم بيانية
- تحليل المبيعات والأرباح
- أفضل العملاء والمنتجات
- تنبيهات المخزون
- إجراءات سريعة

**كيفية الاستخدام**:
1. انقر على "لوحة التحكم المتقدمة" في الشريط الجانبي
2. اختر الفترة الزمنية (اليوم، الأسبوع، الشهر، السنة)
3. استعرض الإحصائيات والرسوم البيانية
4. استخدم الإجراءات السريعة لإنشاء فواتير جديدة

### 🧾 2. نظام الفواتير المتقدم
**الرابط**: `http://localhost:8081/invoices-new`

### 🎮 3. تجربة الفواتير المتقدمة (بيانات وهمية)
**الرابط**: `http://localhost:8081/invoices-demo`
**ملاحظة**: هذه الصفحة تعمل ببيانات وهمية ولا تحتاج لقاعدة بيانات

**الميزات**:
- أنواع فواتير متعددة (بيع، شراء، إرجاع)
- نظام دفع متقدم (نقدي، بطاقة، آجل، مختلط)
- بحث ذكي مع مرشحات
- تقارير تفاعلية
- إدارة الفواتير المعلقة

**كيفية الاستخدام**:

#### إنشاء فاتورة بيع:
1. انتقل إلى تبويب "إدارة الفواتير"
2. انقر "فاتورة جديدة"
3. اختر "فاتورة بيع"
4. اختر العميل
5. أضف المنتجات (يمكن استخدام مسح الباركود)
6. احسب الضرائب والخصومات
7. اختر طريقة الدفع
8. احفظ أو اطبع الفاتورة

#### إنشاء فاتورة شراء:
1. اختر "فاتورة شراء"
2. اختر المورد
3. أضف المنتجات المشتراة
4. احسب التكلفة الإجمالية
5. سجل الدفعة
6. احفظ الفاتورة

#### إرجاع فاتورة:
1. اختر "إرجاع بيع" أو "إرجاع شراء"
2. ابحث عن الفاتورة الأصلية
3. اختر المنتجات المرجعة
4. أدخل سبب الإرجاع
5. احسب المبلغ المسترد
6. احفظ فاتورة الإرجاع

### 🔍 3. البحث الذكي
**الموقع**: تبويب "البحث الذكي" في نظام الفواتير المتقدم

**الميزات**:
- بحث نصي ذكي
- مرشحات متقدمة
- اقتراحات تلقائية
- حفظ معايير البحث

**كيفية الاستخدام**:
1. انتقل إلى تبويب "البحث الذكي"
2. أدخل كلمة البحث (رقم فاتورة، اسم عميل، منتج)
3. استخدم المرشحات المتقدمة:
   - نوع الفاتورة
   - حالة الدفع
   - العميل/المورد
   - فترة زمنية
   - مبلغ معين
4. اعرض النتائج المفصلة

### 📊 4. التقارير الذكية
**الموقع**: تبويب "التقارير" في نظام الفواتير المتقدم

**الميزات**:
- تقارير مالية مفصلة
- رسوم بيانية تفاعلية
- تحليل الاتجاهات
- تقارير العملاء والمنتجات

**كيفية الاستخدام**:
1. انتقل إلى تبويب "التقارير"
2. اختر الفترة الزمنية
3. استعرض التقارير المختلفة:
   - نظرة عامة
   - أفضل العملاء
   - أفضل المنتجات
   - تحليل المبيعات
4. صدر التقارير عند الحاجة

### ⏸️ 5. الفواتير المعلقة
**الموقع**: تبويب "الفواتير المعلقة" في نظام الفواتير المتقدم

**الميزات**:
- تعليق الفواتير غير المكتملة
- استعادة الفواتير المعلقة
- إدارة الفواتير المؤقتة

**كيفية الاستخدام**:
1. عند إنشاء فاتورة، انقر "تعليق" بدلاً من "حفظ"
2. أضف ملاحظات التعليق
3. لاستعادة فاتورة معلقة:
   - انتقل إلى تبويب "الفواتير المعلقة"
   - ابحث عن الفاتورة المطلوبة
   - انقر "استعادة"
   - أكمل الفاتورة واحفظها

### 📱 6. مسح الباركود
**الموقع**: متاح في جميع نماذج إضافة المنتجات

**كيفية الاستخدام**:
1. انقر على أيقونة الكاميرا
2. امنح الصلاحية للوصول للكاميرا
3. وجه الكاميرا نحو الباركود
4. أو أدخل الباركود يدوياً
5. سيتم العثور على المنتج تلقائياً

## 🎨 تخصيص الواجهة:

### العملة:
- العملة الافتراضية: الدينار الجزائري (دج)
- يمكن تغييرها من الإعدادات

### اللغة:
- الواجهة باللغة العربية مع دعم RTL
- التواريخ بالتنسيق العربي

### الألوان:
- تصميم حديث مع ألوان متدرجة
- وضع فاتح مريح للعين

## 🔧 استكشاف الأخطاء:

### إذا لم تظهر الميزات الجديدة:
1. تأكد من تحديث الصفحة (Ctrl+F5)
2. امسح ذاكرة التخزين المؤقت
3. تأكد من تشغيل الخادم على المنفذ الصحيح

### إذا واجهت أخطاء:
1. تحقق من وحدة التحكم في المتصفح (F12)
2. تأكد من اتصال قاعدة البيانات
3. راجع ملف الأخطاء في الخادم

## 📞 الدعم:

للحصول على المساعدة:
1. راجع ملف `FEATURES_NEW.md` للتفاصيل التقنية
2. تحقق من الملفات في مجلد `src/components`
3. استخدم أدوات المطور في المتصفح لتتبع الأخطاء

---

**تهانينا! أصبح لديك نظام إدارة فواتير متقدم وشامل! 🎉**
