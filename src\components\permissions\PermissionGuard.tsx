import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Lock, AlertTriangle, Home } from 'lucide-react';
import { User } from '@/types';

interface PermissionGuardProps {
  user?: User;
  module: string;
  action: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showError?: boolean;
}

interface PermissionCheckProps {
  user?: User;
  module: string;
  action: string;
}

// دالة فحص الصلاحيات
export const hasPermission = ({ user, module, action }: PermissionCheckProps): boolean => {
  // إذا لم يكن هناك مستخدم، لا توجد صلاحيات
  if (!user) return false;

  // المدير العام له جميع الصلاحيات
  if (user.role?.name === 'admin') return true;

  // فحص الصلاحيات من الدور
  const permissions = user.role?.permissions;
  if (!permissions) return false;

  // فحص الصلاحية المحددة
  return permissions[module]?.[action] === true;
};

// مكون حماية الصلاحيات
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  user,
  module,
  action,
  children,
  fallback,
  showError = true
}) => {
  const hasAccess = hasPermission({ user, module, action });

  if (hasAccess) {
    return <>{children}</>;
  }

  // إذا تم تمرير fallback مخصص
  if (fallback) {
    return <>{fallback}</>;
  }

  // إذا كان showError = false، لا نعرض شيئاً
  if (!showError) {
    return null;
  }

  // عرض رسالة خطأ افتراضية
  return (
    <Card className="border-red-200 bg-red-50" dir="rtl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-700">
          <Lock className="w-5 h-5" />
          غير مصرح بالوصول
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Alert variant="destructive">
          <AlertTriangle className="w-4 h-4" />
          <AlertDescription>
            ليس لديك صلاحية للوصول إلى هذه الميزة. يرجى التواصل مع المدير لطلب الصلاحيات المناسبة.
          </AlertDescription>
        </Alert>
        
        <div className="mt-4 text-sm text-gray-600">
          <p><strong>الصلاحية المطلوبة:</strong> {getModuleName(module)} - {getActionName(action)}</p>
          <p><strong>دورك الحالي:</strong> {user?.role?.name_ar || 'غير محدد'}</p>
        </div>

        <div className="mt-4">
          <Button variant="outline" onClick={() => window.history.back()}>
            <Home className="w-4 h-4 ml-2" />
            العودة
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// دالة للحصول على اسم الوحدة بالعربية
const getModuleName = (module: string): string => {
  const moduleNames: Record<string, string> = {
    invoices: 'الفواتير',
    products: 'المنتجات',
    customers: 'العملاء',
    suppliers: 'الموردين',
    reports: 'التقارير',
    settings: 'الإعدادات',
    users: 'المستخدمين'
  };
  return moduleNames[module] || module;
};

// دالة للحصول على اسم الإجراء بالعربية
const getActionName = (action: string): string => {
  const actionNames: Record<string, string> = {
    create: 'إنشاء',
    read: 'عرض',
    update: 'تعديل',
    delete: 'حذف',
    print: 'طباعة',
    return: 'إرجاع',
    suspend: 'تعليق',
    manage_stock: 'إدارة المخزون',
    view_balance: 'عرض الرصيد',
    export: 'تصدير',
    financial: 'التقارير المالية',
    inventory: 'تقارير المخزون',
    backup: 'النسخ الاحتياطي',
    restore: 'الاستعادة',
    manage_roles: 'إدارة الأدوار'
  };
  return actionNames[action] || action;
};

// مكون لعرض حالة الصلاحيات
export const PermissionStatus: React.FC<{ user?: User }> = ({ user }) => {
  if (!user) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="w-4 h-4" />
        <AlertDescription>
          لم يتم تسجيل الدخول
        </AlertDescription>
      </Alert>
    );
  }

  if (!user.is_active) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="w-4 h-4" />
        <AlertDescription>
          حسابك معطل. يرجى التواصل مع المدير.
        </AlertDescription>
      </Alert>
    );
  }

  if (!user.role) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="w-4 h-4" />
        <AlertDescription>
          لم يتم تعيين دور لحسابك. يرجى التواصل مع المدير.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Alert>
      <Shield className="w-4 h-4" />
      <AlertDescription>
        مرحباً {user.full_name}، أنت مسجل كـ {user.role.name_ar}
      </AlertDescription>
    </Alert>
  );
};

// Hook لاستخدام الصلاحيات
export const usePermissions = (user?: User) => {
  const checkPermission = (module: string, action: string): boolean => {
    return hasPermission({ user, module, action });
  };

  const canCreateInvoices = () => checkPermission('invoices', 'create');
  const canEditInvoices = () => checkPermission('invoices', 'update');
  const canDeleteInvoices = () => checkPermission('invoices', 'delete');
  const canPrintInvoices = () => checkPermission('invoices', 'print');
  const canReturnInvoices = () => checkPermission('invoices', 'return');
  const canSuspendInvoices = () => checkPermission('invoices', 'suspend');

  const canManageProducts = () => checkPermission('products', 'create');
  const canViewProducts = () => checkPermission('products', 'read');
  const canManageStock = () => checkPermission('products', 'manage_stock');

  const canManageCustomers = () => checkPermission('customers', 'create');
  const canViewCustomers = () => checkPermission('customers', 'read');
  const canViewCustomerBalance = () => checkPermission('customers', 'view_balance');

  const canManageSuppliers = () => checkPermission('suppliers', 'create');
  const canViewSuppliers = () => checkPermission('suppliers', 'read');
  const canViewSupplierBalance = () => checkPermission('suppliers', 'view_balance');

  const canViewReports = () => checkPermission('reports', 'view');
  const canExportReports = () => checkPermission('reports', 'export');
  const canViewFinancialReports = () => checkPermission('reports', 'financial');
  const canViewInventoryReports = () => checkPermission('reports', 'inventory');

  const canManageSettings = () => checkPermission('settings', 'update');
  const canBackupRestore = () => checkPermission('settings', 'backup');

  const canManageUsers = () => checkPermission('users', 'create');
  const canManageRoles = () => checkPermission('users', 'manage_roles');

  const isAdmin = () => user?.role?.name === 'admin';
  const isCashier = () => user?.role?.name === 'cashier';
  const isAccountant = () => user?.role?.name === 'accountant';
  const isWarehouse = () => user?.role?.name === 'warehouse';

  return {
    checkPermission,
    canCreateInvoices,
    canEditInvoices,
    canDeleteInvoices,
    canPrintInvoices,
    canReturnInvoices,
    canSuspendInvoices,
    canManageProducts,
    canViewProducts,
    canManageStock,
    canManageCustomers,
    canViewCustomers,
    canViewCustomerBalance,
    canManageSuppliers,
    canViewSuppliers,
    canViewSupplierBalance,
    canViewReports,
    canExportReports,
    canViewFinancialReports,
    canViewInventoryReports,
    canManageSettings,
    canBackupRestore,
    canManageUsers,
    canManageRoles,
    isAdmin,
    isCashier,
    isAccountant,
    isWarehouse
  };
};

// مكون لعرض الصلاحيات المتاحة للمستخدم
export const UserPermissionsList: React.FC<{ user?: User }> = ({ user }) => {
  const permissions = usePermissions(user);

  if (!user || !user.role) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="w-4 h-4" />
        <AlertDescription>
          لا توجد صلاحيات متاحة
        </AlertDescription>
      </Alert>
    );
  }

  const availablePermissions = [
    { check: permissions.canCreateInvoices, label: 'إنشاء الفواتير' },
    { check: permissions.canEditInvoices, label: 'تعديل الفواتير' },
    { check: permissions.canDeleteInvoices, label: 'حذف الفواتير' },
    { check: permissions.canPrintInvoices, label: 'طباعة الفواتير' },
    { check: permissions.canReturnInvoices, label: 'إرجاع الفواتير' },
    { check: permissions.canSuspendInvoices, label: 'تعليق الفواتير' },
    { check: permissions.canManageProducts, label: 'إدارة المنتجات' },
    { check: permissions.canManageStock, label: 'إدارة المخزون' },
    { check: permissions.canManageCustomers, label: 'إدارة العملاء' },
    { check: permissions.canManageSuppliers, label: 'إدارة الموردين' },
    { check: permissions.canViewReports, label: 'عرض التقارير' },
    { check: permissions.canExportReports, label: 'تصدير التقارير' },
    { check: permissions.canManageSettings, label: 'إدارة الإعدادات' },
    { check: permissions.canManageUsers, label: 'إدارة المستخدمين' }
  ].filter(p => p.check());

  return (
    <Card dir="rtl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="w-5 h-5" />
          صلاحياتك الحالية
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="text-sm text-gray-600 mb-3">
            الدور: <strong>{user.role.name_ar}</strong>
          </div>
          
          {availablePermissions.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {availablePermissions.map((permission, index) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>{permission.label}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-sm text-gray-500">
              لا توجد صلاحيات محددة
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PermissionGuard;
