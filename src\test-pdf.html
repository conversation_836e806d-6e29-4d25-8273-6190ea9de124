<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار PDF العربي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #ccc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
        }
        .invoice-info {
            text-align: left;
        }
        .customer-info {
            margin: 20px 0;
        }
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .products-table th,
        .products-table td {
            text-align: right;
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        .products-table th {
            background-color: #f5f5f5;
        }
        .summary {
            margin-top: 20px;
            text-align: left;
        }
        .thank-you {
            margin-top: 40px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="header">
            <div class="company-name">دي زد - إدارة المخزون</div>
            <div class="invoice-info">
                <div style="font-size: 16px; margin-bottom: 5px;">فاتورة INV-0001</div>
                <div>تاريخ: 17/06/2025</div>
            </div>
        </div>
        
        <div class="customer-info">
            <div style="margin-bottom: 5px;"><strong>العميل:</strong> هشام تمام</div>
            <div style="margin-bottom: 5px;"><strong>رقم الهاتف:</strong> 0549876907</div>
            <div style="margin-bottom: 5px;"><strong>البريد الإلكتروني:</strong> <EMAIL></div>
        </div>
        
        <table class="products-table">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>المجموع</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>منتج تجريبي 1</td>
                    <td>10</td>
                    <td>500.00 دج</td>
                    <td>5000.00 دج</td>
                </tr>
                <tr>
                    <td>منتج تجريبي 2</td>
                    <td>5</td>
                    <td>250.00 دج</td>
                    <td>1250.00 دج</td>
                </tr>
            </tbody>
        </table>
        
        <div class="summary">
            <div style="margin-bottom: 5px;"><strong>المجموع:</strong> 6250.00 دج</div>
            <div style="margin-bottom: 5px;"><strong>المدفوع:</strong> 6000.00 دج</div>
            <div style="margin-bottom: 5px;"><strong>المتبقي:</strong> 250.00 دج</div>
            <div style="margin-bottom: 5px;"><strong>طريقة الدفع:</strong> نقداً</div>
            <div style="margin-bottom: 5px;"><strong>حالة الدفع:</strong> مدفوع جزئياً</div>
        </div>
        
        <div class="thank-you">
            <p>شكراً لك</p>
        </div>
    </div>

    <script>
        // اختبار تحويل التاريخ إلى الفرنسية
        const testDate = new Date('2025-06-17');
        const frenchDate = testDate.toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit', 
            year: 'numeric'
        });
        console.log('التاريخ بالفرنسية:', frenchDate);
        
        // تحديث التاريخ في الصفحة
        document.querySelector('.invoice-info div:last-child').textContent = `تاريخ: ${frenchDate}`;
    </script>
</body>
</html>
