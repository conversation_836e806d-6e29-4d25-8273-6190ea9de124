# 🚀 تشغيل سريع - نظام إدارة الفواتير

## ⚡ تشغيل فوري (3 خطوات)

### 1. افتح Command Prompt
- اضغط `Win + R`
- اكتب `cmd`
- اضغط Enter

### 2. انتقل للمجلد وشغل الخادم
```cmd
cd "C:\Users\<USER>\Desktop\dz\dz-store-manager-web"
npm run dev
```

### 3. افتح المتصفح
انتقل إلى: `http://localhost:8080`

## 🎯 الصفحات المتاحة

### 🎮 الصفحات التجريبية (الأفضل للاختبار)

#### لوحة التحكم التجريبية ⭐
```
http://localhost:8080/dashboard-demo
```
**الميزات:**
- إحصائيات تفاعلية
- رسوم بيانية للمبيعات
- أفضل العملاء والمنتجات
- تنبيهات المخزون

#### نظام الفواتير التجريبي ⭐
```
http://localhost:8080/invoices-demo
```
**الميزات:**
- إنشاء فواتير (بيع، شراء، إرجاع)
- نظام دفع متقدم
- بحث ذكي
- تقارير تفاعلية

### 🔧 الصفحات المتقدمة (تحتاج قاعدة بيانات)

#### لوحة التحكم الكاملة
```
http://localhost:8080/dashboard
```

#### نظام الفواتير الكامل
```
http://localhost:8080/invoices-new
```

#### إدارة البيانات
- `/products` - المنتجات
- `/customers` - العملاء
- `/suppliers` - الموردين
- `/inventory` - المخزون
- `/settings` - الإعدادات

## 🎨 ما ستراه

### في لوحة التحكم التجريبية:
- ✅ 4 بطاقات ملونة للإحصائيات
- ✅ رسم بياني للمبيعات اليومية
- ✅ رسم دائري لحالة الدفع
- ✅ جداول أفضل العملاء والمنتجات
- ✅ تنبيهات المخزون

### في نظام الفواتير التجريبي:
- ✅ 4 تبويبات: الفواتير، البحث، التقارير، المعلقة
- ✅ إحصائيات ملونة
- ✅ نماذج تفاعلية
- ✅ جداول منظمة

## 🔧 حل المشاكل

### إذا لم يعمل npm:
```cmd
npm install
npm run dev
```

### إذا كان المنفذ مشغولاً:
جرب: `http://localhost:8081/` أو `http://localhost:3000/`

### إذا ظهرت أخطاء:
- حدث الصفحة: `Ctrl + F5`
- أو امسح الكاش: `Ctrl + Shift + R`

## 🎉 النتيجة

**✅ نظام إدارة فواتير متقدم جاهز للاستخدام!**

### المميزات:
- 🇩🇿 دعم العملة الجزائرية (دج)
- 🔍 بحث ذكي متقدم
- 📊 تقارير تفاعلية
- 💳 نظام دفع مرن
- 📱 تصميم متجاوب
- 🎨 واجهة عربية جميلة

---

**🚀 استمتع بالنظام الجديد! 🇩🇿**
