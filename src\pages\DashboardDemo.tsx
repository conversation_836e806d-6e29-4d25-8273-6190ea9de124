import React from 'react';
import { Layout } from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  ShoppingCart, 
  Users, 
  Package,
  AlertTriangle,
  Calendar,
  Clock,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Star,
  ArrowRight
} from 'lucide-react';
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Pie<PERSON>hart as RechartsPieChart,
  <PERSON>,
  Cell,
  <PERSON><PERSON>hart,
  Bar
} from 'recharts';
import { formatCurrency } from '@/utils/currency';

const DashboardDemo = () => {
  // بيانات وهمية للاختبار
  const mockData = {
    totalSales: 125000,
    totalPurchases: 85000,
    grossProfit: 40000,
    pendingAmount: 15000,
    invoicesCount: 45,
    lowStockProducts: 8,
    outOfStockProducts: 3
  };

  // بيانات المبيعات اليومية
  const dailySales = [
    { date: 'السبت', sales: 12000, invoices: 8 },
    { date: 'الأحد', sales: 15000, invoices: 12 },
    { date: 'الاثنين', sales: 18000, invoices: 15 },
    { date: 'الثلاثاء', sales: 22000, invoices: 18 },
    { date: 'الأربعاء', sales: 19000, invoices: 14 },
    { date: 'الخميس', sales: 25000, invoices: 20 },
    { date: 'الجمعة', sales: 14000, invoices: 10 }
  ];

  // توزيع حالة الدفع
  const paymentStatus = [
    { name: 'مدفوع', value: 30, color: '#10B981' },
    { name: 'جزئي', value: 10, color: '#F59E0B' },
    { name: 'غير مدفوع', value: 5, color: '#EF4444' }
  ];

  // أفضل العملاء
  const topCustomers = [
    { name: 'أحمد محمد', phone: '0555123456', total: 25000 },
    { name: 'فاطمة علي', phone: '0555654321', total: 18000 },
    { name: 'محمد حسن', phone: '0555789012', total: 15000 },
    { name: 'سارة أحمد', phone: '0555345678', total: 12000 },
    { name: 'علي محمود', phone: '0555901234', total: 10000 }
  ];

  // أفضل المنتجات
  const topProducts = [
    { name: 'لابتوب ديل', quantity: 15, revenue: 37500 },
    { name: 'ماوس لاسلكي', quantity: 45, revenue: 3375 },
    { name: 'كيبورد ميكانيكي', quantity: 20, revenue: 6000 },
    { name: 'شاشة سامسونج', quantity: 8, revenue: 12000 },
    { name: 'طابعة HP', quantity: 5, revenue: 7500 }
  ];

  // تنبيهات المخزون
  const stockAlerts = [
    { name: 'لابتوب ديل', stock: 0, status: 'نفد' },
    { name: 'ماوس لاسلكي', stock: 2, status: 'منخفض' },
    { name: 'كيبورد ميكانيكي', stock: 1, status: 'منخفض' }
  ];

  return (
    <Layout>
      <div style={{
        padding: '0',
        maxWidth: '100%',
        overflow: 'hidden'
      }}>
        {/* رأس لوحة التحكم العصري */}
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '20px',
          padding: '2rem',
          marginBottom: '2rem',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* خلفية متحركة */}
          <div style={{
            position: 'absolute',
            top: '-50%',
            right: '-50%',
            width: '200%',
            height: '200%',
            background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
            animation: 'float 8s ease-in-out infinite'
          }} />

          <div style={{
            position: 'relative',
            zIndex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
            gap: '1rem'
          }}>
            <div>
              <h1 style={{
                fontSize: '2.5rem',
                fontWeight: '700',
                margin: '0 0 0.5rem 0',
                textShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                🏪 لوحة التحكم العصرية
              </h1>
              <p style={{
                fontSize: '1.1rem',
                opacity: 0.9,
                margin: 0
              }}>
                مرحباً أحمد، إليك ملخص أعمالك اليوم • {new Date().toLocaleDateString('ar-DZ')}
              </p>
            </div>

            <div style={{
              display: 'flex',
              gap: '1rem',
              alignItems: 'center'
            }}>
              <select style={{
                padding: '12px 16px',
                border: 'none',
                borderRadius: '12px',
                background: 'rgba(255,255,255,0.2)',
                color: 'white',
                fontSize: '0.875rem',
                outline: 'none',
                backdropFilter: 'blur(10px)',
                cursor: 'pointer'
              }}>
                <option value="today" style={{ color: '#333' }}>اليوم</option>
                <option value="week" style={{ color: '#333' }}>هذا الأسبوع</option>
                <option value="month" style={{ color: '#333' }}>هذا الشهر</option>
                <option value="year" style={{ color: '#333' }}>هذا العام</option>
              </select>

              <button style={{
                padding: '12px 20px',
                border: 'none',
                borderRadius: '12px',
                background: 'rgba(255,255,255,0.2)',
                color: 'white',
                fontSize: '0.875rem',
                cursor: 'pointer',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease'
              }}>
                📊 تقرير مفصل
              </button>
            </div>
          </div>
        </div>

        {/* البطاقات الرئيسية العصرية */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '1.5rem',
          marginBottom: '2rem'
        }}>
          {/* بطاقة المبيعات */}
          <div style={{
            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
            borderRadius: '20px',
            padding: '2rem',
            color: 'white',
            position: 'relative',
            overflow: 'hidden',
            boxShadow: '0 10px 30px rgba(16, 185, 129, 0.3)',
            transform: 'translateY(0)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-5px)';
            e.currentTarget.style.boxShadow = '0 20px 40px rgba(16, 185, 129, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 10px 30px rgba(16, 185, 129, 0.3)';
          }}
          >
            <div style={{
              position: 'absolute',
              top: '-20px',
              right: '-20px',
              width: '100px',
              height: '100px',
              background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
              borderRadius: '50%'
            }} />
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              position: 'relative',
              zIndex: 1
            }}>
              <div>
                <p style={{
                  fontSize: '0.875rem',
                  opacity: 0.8,
                  margin: '0 0 0.5rem 0'
                }}>
                  💰 صافي المبيعات
                </p>
                <p style={{
                  fontSize: '2.5rem',
                  fontWeight: '700',
                  margin: '0 0 0.5rem 0'
                }}>
                  {formatCurrency(mockData.totalSales)}
                </p>
                <p style={{
                  fontSize: '0.875rem',
                  opacity: 0.8,
                  margin: 0
                }}>
                  📄 {mockData.invoicesCount} فاتورة
                </p>
              </div>
              <div style={{
                width: '60px',
                height: '60px',
                background: 'rgba(255,255,255,0.2)',
                borderRadius: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(10px)'
              }}>
                <TrendingUp size={28} />
              </div>
            </div>
          </div>

          {/* بطاقة الربح */}
          <div style={{
            background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
            borderRadius: '20px',
            padding: '2rem',
            color: 'white',
            position: 'relative',
            overflow: 'hidden',
            boxShadow: '0 10px 30px rgba(59, 130, 246, 0.3)',
            transform: 'translateY(0)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-5px)';
            e.currentTarget.style.boxShadow = '0 20px 40px rgba(59, 130, 246, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 10px 30px rgba(59, 130, 246, 0.3)';
          }}
          >
            <div style={{
              position: 'absolute',
              top: '-20px',
              right: '-20px',
              width: '100px',
              height: '100px',
              background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
              borderRadius: '50%'
            }} />
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              position: 'relative',
              zIndex: 1
            }}>
              <div>
                <p style={{
                  fontSize: '0.875rem',
                  opacity: 0.8,
                  margin: '0 0 0.5rem 0'
                }}>
                  🎯 الربح الإجمالي
                </p>
                <p style={{
                  fontSize: '2.5rem',
                  fontWeight: '700',
                  margin: '0 0 0.5rem 0'
                }}>
                  {formatCurrency(mockData.grossProfit)}
                </p>
                <p style={{
                  fontSize: '0.875rem',
                  opacity: 0.8,
                  margin: 0
                }}>
                  📈 هامش {((mockData.grossProfit / mockData.totalSales) * 100).toFixed(1)}%
                </p>
              </div>
              <div style={{
                width: '60px',
                height: '60px',
                background: 'rgba(255,255,255,0.2)',
                borderRadius: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(10px)'
              }}>
                <Target size={28} />
              </div>
            </div>
          </div>

          {/* بطاقة المبالغ المعلقة */}
          <div style={{
            background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
            borderRadius: '20px',
            padding: '2rem',
            color: 'white',
            position: 'relative',
            overflow: 'hidden',
            boxShadow: '0 10px 30px rgba(245, 158, 11, 0.3)',
            transform: 'translateY(0)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-5px)';
            e.currentTarget.style.boxShadow = '0 20px 40px rgba(245, 158, 11, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 10px 30px rgba(245, 158, 11, 0.3)';
          }}
          >
            <div style={{
              position: 'absolute',
              top: '-20px',
              right: '-20px',
              width: '100px',
              height: '100px',
              background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
              borderRadius: '50%'
            }} />
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              position: 'relative',
              zIndex: 1
            }}>
              <div>
                <p style={{
                  fontSize: '0.875rem',
                  opacity: 0.8,
                  margin: '0 0 0.5rem 0'
                }}>
                  ⏰ المبالغ المعلقة
                </p>
                <p style={{
                  fontSize: '2.5rem',
                  fontWeight: '700',
                  margin: '0 0 0.5rem 0'
                }}>
                  {formatCurrency(mockData.pendingAmount)}
                </p>
                <p style={{
                  fontSize: '0.875rem',
                  opacity: 0.8,
                  margin: 0
                }}>
                  📊 {((mockData.pendingAmount / mockData.totalSales) * 100).toFixed(1)}% من المبيعات
                </p>
              </div>
              <div style={{
                width: '60px',
                height: '60px',
                background: 'rgba(255,255,255,0.2)',
                borderRadius: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(10px)'
              }}>
                <Clock size={28} />
              </div>
            </div>
          </div>

          {/* بطاقة تنبيهات المخزون */}
          <div style={{
            background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
            borderRadius: '20px',
            padding: '2rem',
            color: 'white',
            position: 'relative',
            overflow: 'hidden',
            boxShadow: '0 10px 30px rgba(239, 68, 68, 0.3)',
            transform: 'translateY(0)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-5px)';
            e.currentTarget.style.boxShadow = '0 20px 40px rgba(239, 68, 68, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 10px 30px rgba(239, 68, 68, 0.3)';
          }}
          >
            <div style={{
              position: 'absolute',
              top: '-20px',
              right: '-20px',
              width: '100px',
              height: '100px',
              background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
              borderRadius: '50%'
            }} />
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              position: 'relative',
              zIndex: 1
            }}>
              <div>
                <p style={{
                  fontSize: '0.875rem',
                  opacity: 0.8,
                  margin: '0 0 0.5rem 0'
                }}>
                  ⚠️ تنبيهات المخزون
                </p>
                <p style={{
                  fontSize: '2.5rem',
                  fontWeight: '700',
                  margin: '0 0 0.5rem 0'
                }}>
                  {mockData.lowStockProducts}
                </p>
                <p style={{
                  fontSize: '0.875rem',
                  opacity: 0.8,
                  margin: 0
                }}>
                  🚫 {mockData.outOfStockProducts} نفد تماماً
                </p>
              </div>
              <div style={{
                width: '60px',
                height: '60px',
                background: 'rgba(255,255,255,0.2)',
                borderRadius: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(10px)'
              }}>
                <AlertTriangle size={28} />
              </div>
            </div>
          </div>
        </div>

        {/* الرسوم البيانية العصرية */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
          gap: '2rem',
          marginBottom: '2rem'
        }}>
          {/* الرسم البياني للمبيعات */}
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '2rem',
            boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
            border: '1px solid rgba(0,0,0,0.05)'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              marginBottom: '2rem'
            }}>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white'
              }}>
                <BarChart3 size={24} />
              </div>
              <div>
                <h3 style={{
                  fontSize: '1.5rem',
                  fontWeight: '700',
                  margin: 0,
                  color: '#1f2937'
                }}>
                  📈 اتجاه المبيعات
                </h3>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  margin: '0.25rem 0 0 0'
                }}>
                  آخر 7 أيام • إجمالي {formatCurrency(dailySales.reduce((sum, day) => sum + day.sales, 0))}
                </p>
              </div>
            </div>
            <ResponsiveContainer width="100%" height={350}>
              <AreaChart data={dailySales}>
                <defs>
                  <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                <XAxis
                  dataKey="date"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: '#6b7280', fontSize: 12 }}
                  tickFormatter={(value) => `${value / 1000}ك`}
                />
                <Tooltip
                  formatter={(value) => [formatCurrency(Number(value)), 'المبيعات']}
                  labelFormatter={(label) => `يوم ${label}`}
                  contentStyle={{
                    backgroundColor: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="sales"
                  stroke="#10b981"
                  strokeWidth={3}
                  fill="url(#salesGradient)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>

          {/* توزيع حالة الدفع */}
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '2rem',
            boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
            border: '1px solid rgba(0,0,0,0.05)'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              marginBottom: '2rem'
            }}>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white'
              }}>
                <PieChart size={24} />
              </div>
              <div>
                <h3 style={{
                  fontSize: '1.25rem',
                  fontWeight: '700',
                  margin: 0,
                  color: '#1f2937'
                }}>
                  💳 حالة الدفع
                </h3>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  margin: '0.25rem 0 0 0'
                }}>
                  توزيع الفواتير
                </p>
              </div>
            </div>

            <ResponsiveContainer width="100%" height={250}>
              <RechartsPieChart>
                <Pie
                  data={paymentStatus}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {paymentStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
                  }}
                />
              </RechartsPieChart>
            </ResponsiveContainer>

            {/* إحصائيات سريعة */}
            <div style={{
              marginTop: '1rem',
              display: 'flex',
              flexDirection: 'column',
              gap: '0.75rem'
            }}>
              {paymentStatus.map((status, index) => (
                <div key={index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '0.75rem',
                  background: '#f9fafb',
                  borderRadius: '8px'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    <div style={{
                      width: '12px',
                      height: '12px',
                      background: status.color,
                      borderRadius: '50%'
                    }} />
                    <span style={{
                      fontSize: '0.875rem',
                      color: '#374151'
                    }}>
                      {status.name}
                    </span>
                  </div>
                  <span style={{
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    color: '#1f2937'
                  }}>
                    {status.value} فاتورة
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* الجداول */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* أفضل العملاء */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                أفضل العملاء
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topCustomers.map((customer, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div>
                        <div className="font-medium">{customer.name}</div>
                        <div className="text-sm text-gray-500">{customer.phone}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-green-600">
                        {formatCurrency(customer.total)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* أفضل المنتجات */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                أفضل المنتجات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topProducts.map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div>
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-gray-500">
                          {product.quantity} قطعة مباعة
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-blue-600">
                        {formatCurrency(product.revenue)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* تنبيهات المخزون */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                تنبيهات المخزون
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stockAlerts.map((alert, index) => (
                  <div 
                    key={index} 
                    className={`flex items-center justify-between p-3 border-l-4 rounded-lg ${
                      alert.status === 'نفد' 
                        ? 'border-l-red-500 bg-red-50' 
                        : 'border-l-yellow-500 bg-yellow-50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <AlertTriangle 
                        className={`w-5 h-5 ${
                          alert.status === 'نفد' ? 'text-red-500' : 'text-yellow-500'
                        }`} 
                      />
                      <div>
                        <div className="font-medium">{alert.name}</div>
                        <div className={`text-sm ${
                          alert.status === 'نفد' ? 'text-red-600' : 'text-yellow-600'
                        }`}>
                          {alert.status === 'نفد' ? 'نفد من المخزون' : `مخزون منخفض: ${alert.stock} متبقي`}
                        </div>
                      </div>
                    </div>
                    <Badge variant={alert.status === 'نفد' ? 'destructive' : 'secondary'}>
                      {alert.status === 'نفد' ? 'نفد' : alert.stock}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
        {/* إضافة الأنيميشن */}
        <style>{`
          @keyframes float {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            33% { transform: translate(10px, -10px) rotate(1deg); }
            66% { transform: translate(-5px, 5px) rotate(-1deg); }
          }

          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(30px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
          }
        `}</style>
      </div>
    </Layout>
  );
};

export default DashboardDemo;
