import React from 'react';
import { Layout } from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  ShoppingCart, 
  Users, 
  Package,
  AlertTriangle,
  Calendar,
  Clock,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Star,
  ArrowRight
} from 'lucide-react';
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Pie<PERSON>hart as RechartsPieChart,
  <PERSON>,
  Cell,
  <PERSON><PERSON>hart,
  Bar
} from 'recharts';
import { formatCurrency } from '@/utils/currency';

const DashboardDemo = () => {
  // بيانات وهمية للاختبار
  const mockData = {
    totalSales: 125000,
    totalPurchases: 85000,
    grossProfit: 40000,
    pendingAmount: 15000,
    invoicesCount: 45,
    lowStockProducts: 8,
    outOfStockProducts: 3
  };

  // بيانات المبيعات اليومية
  const dailySales = [
    { date: 'السبت', sales: 12000, invoices: 8 },
    { date: 'الأحد', sales: 15000, invoices: 12 },
    { date: 'الاثنين', sales: 18000, invoices: 15 },
    { date: 'الثلاثاء', sales: 22000, invoices: 18 },
    { date: 'الأربعاء', sales: 19000, invoices: 14 },
    { date: 'الخميس', sales: 25000, invoices: 20 },
    { date: 'الجمعة', sales: 14000, invoices: 10 }
  ];

  // توزيع حالة الدفع
  const paymentStatus = [
    { name: 'مدفوع', value: 30, color: '#10B981' },
    { name: 'جزئي', value: 10, color: '#F59E0B' },
    { name: 'غير مدفوع', value: 5, color: '#EF4444' }
  ];

  // أفضل العملاء
  const topCustomers = [
    { name: 'أحمد محمد', phone: '0555123456', total: 25000 },
    { name: 'فاطمة علي', phone: '0555654321', total: 18000 },
    { name: 'محمد حسن', phone: '0555789012', total: 15000 },
    { name: 'سارة أحمد', phone: '0555345678', total: 12000 },
    { name: 'علي محمود', phone: '0555901234', total: 10000 }
  ];

  // أفضل المنتجات
  const topProducts = [
    { name: 'لابتوب ديل', quantity: 15, revenue: 37500 },
    { name: 'ماوس لاسلكي', quantity: 45, revenue: 3375 },
    { name: 'كيبورد ميكانيكي', quantity: 20, revenue: 6000 },
    { name: 'شاشة سامسونج', quantity: 8, revenue: 12000 },
    { name: 'طابعة HP', quantity: 5, revenue: 7500 }
  ];

  // تنبيهات المخزون
  const stockAlerts = [
    { name: 'لابتوب ديل', stock: 0, status: 'نفد' },
    { name: 'ماوس لاسلكي', stock: 2, status: 'منخفض' },
    { name: 'كيبورد ميكانيكي', stock: 1, status: 'منخفض' }
  ];

  return (
    <Layout>
      <div className="space-y-6" dir="rtl">
        {/* رأس لوحة التحكم */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">لوحة التحكم - وضع التجربة</h1>
            <p className="text-gray-600">
              مرحباً، إليك ملخص أعمالك (بيانات وهمية للاختبار)
            </p>
          </div>
          
          <div className="flex gap-2">
            <select className="p-2 border rounded-lg">
              <option value="today">اليوم</option>
              <option value="week">هذا الأسبوع</option>
              <option value="month">هذا الشهر</option>
              <option value="year">هذا العام</option>
            </select>
          </div>
        </div>

        {/* البطاقات الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">صافي المبيعات</p>
                  <p className="text-3xl font-bold">
                    {formatCurrency(mockData.totalSales)}
                  </p>
                  <p className="text-sm text-green-100">
                    {mockData.invoicesCount} فاتورة
                  </p>
                </div>
                <TrendingUp className="w-12 h-12 text-green-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">الربح الإجمالي</p>
                  <p className="text-3xl font-bold">
                    {formatCurrency(mockData.grossProfit)}
                  </p>
                  <p className="text-sm text-blue-100">
                    هامش {((mockData.grossProfit / mockData.totalSales) * 100).toFixed(1)}%
                  </p>
                </div>
                <Target className="w-12 h-12 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">المبالغ المعلقة</p>
                  <p className="text-3xl font-bold">
                    {formatCurrency(mockData.pendingAmount)}
                  </p>
                  <p className="text-sm text-orange-100">
                    {((mockData.pendingAmount / mockData.totalSales) * 100).toFixed(1)}% من المبيعات
                  </p>
                </div>
                <Clock className="w-12 h-12 text-orange-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-500 to-red-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-100">تنبيهات المخزون</p>
                  <p className="text-3xl font-bold">
                    {mockData.lowStockProducts}
                  </p>
                  <p className="text-sm text-red-100">
                    {mockData.outOfStockProducts} نفد تماماً
                  </p>
                </div>
                <AlertTriangle className="w-12 h-12 text-red-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* المحتوى الرئيسي */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* الرسم البياني للمبيعات */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                اتجاه المبيعات (آخر 7 أيام)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={dailySales}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value) => [formatCurrency(Number(value)), 'المبيعات']}
                    labelFormatter={(label) => `يوم ${label}`}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="sales" 
                    stroke="#10B981" 
                    fill="#10B981" 
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* توزيع حالة الدفع */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="w-5 h-5" />
                حالة الدفع
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Pie
                    data={paymentStatus}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {paymentStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </RechartsPieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* الجداول */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* أفضل العملاء */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                أفضل العملاء
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topCustomers.map((customer, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div>
                        <div className="font-medium">{customer.name}</div>
                        <div className="text-sm text-gray-500">{customer.phone}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-green-600">
                        {formatCurrency(customer.total)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* أفضل المنتجات */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                أفضل المنتجات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topProducts.map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div>
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-gray-500">
                          {product.quantity} قطعة مباعة
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-blue-600">
                        {formatCurrency(product.revenue)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* تنبيهات المخزون */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                تنبيهات المخزون
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stockAlerts.map((alert, index) => (
                  <div 
                    key={index} 
                    className={`flex items-center justify-between p-3 border-l-4 rounded-lg ${
                      alert.status === 'نفد' 
                        ? 'border-l-red-500 bg-red-50' 
                        : 'border-l-yellow-500 bg-yellow-50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <AlertTriangle 
                        className={`w-5 h-5 ${
                          alert.status === 'نفد' ? 'text-red-500' : 'text-yellow-500'
                        }`} 
                      />
                      <div>
                        <div className="font-medium">{alert.name}</div>
                        <div className={`text-sm ${
                          alert.status === 'نفد' ? 'text-red-600' : 'text-yellow-600'
                        }`}>
                          {alert.status === 'نفد' ? 'نفد من المخزون' : `مخزون منخفض: ${alert.stock} متبقي`}
                        </div>
                      </div>
                    </div>
                    <Badge variant={alert.status === 'نفد' ? 'destructive' : 'secondary'}>
                      {alert.status === 'نفد' ? 'نفد' : alert.stock}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default DashboardDemo;
