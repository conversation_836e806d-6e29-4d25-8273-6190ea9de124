/* تخطيط بسيط وثابت */
.sidebar-layout {
  display: flex !important;
  min-height: 100vh !important;
  direction: rtl !important;
  background: #f9fafb !important;
}

.main-content {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  min-width: 0 !important;
  overflow: hidden !important;
}

.sidebar-container {
  width: 320px !important;
  flex-shrink: 0 !important;
  background: white !important;
  border-left: 1px solid #e5e7eb !important;
  height: 100vh !important;
  overflow-y: auto !important;
}

/* الشريط الجانبي */
[data-sidebar="sidebar"] {
  position: static !important;
  height: 100% !important;
  width: 100% !important;
  border: none !important;
  box-shadow: none !important;
}

/* الهيدر */
header {
  background: white !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 1rem 1.5rem !important;
  flex-shrink: 0 !important;
  margin: 0 !important;
}

/* المحتوى الرئيسي */
main {
  flex: 1 !important;
  padding: 1.5rem !important;
  overflow-y: auto !important;
  background: #f9fafb !important;
}

/* إزالة أي تداخل */
.space-y-6 > * + * {
  margin-top: 1.5rem !important;
}

.grid {
  display: grid !important;
  gap: 1.5rem !important;
}