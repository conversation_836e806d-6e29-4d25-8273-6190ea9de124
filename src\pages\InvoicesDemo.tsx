import React, { useState } from 'react';
import { Layout } from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  FileText, 
  ShoppingCart, 
  Package, 
  RotateCcw, 
  Search,
  BarChart3,
  Settings,
  Users,
  Truck
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// استيراد المكونات الجديدة
import InvoiceManager from "@/components/invoices/InvoiceManager";
import SmartSearch from "@/components/search/SmartSearch";
import IntelligentReports from "@/components/reports/IntelligentReports";
import SuspendedInvoices from "@/components/invoices/SuspendedInvoices";
import { Invoice, Customer, Supplier, Product, User, SuspendedInvoice } from "@/types";
import { formatCurrency } from "@/utils/currency";

const InvoicesDemo = () => {
  const [activeTab, setActiveTab] = useState('invoices');
  const [searchResults, setSearchResults] = useState<Invoice[]>([]);
  const { toast } = useToast();

  // بيانات وهمية للاختبار
  const mockCustomers: Customer[] = [
    {
      id: '1',
      name: 'أحمد محمد',
      phone: '0555123456',
      email: '<EMAIL>',
      address: 'الرياض، المملكة العربية السعودية',
      balance: 1500,
      totalPurchases: 5000,
      createdAt: new Date()
    },
    {
      id: '2',
      name: 'فاطمة علي',
      phone: '0555654321',
      email: '<EMAIL>',
      address: 'جدة، المملكة العربية السعودية',
      balance: 0,
      totalPurchases: 3000,
      createdAt: new Date()
    }
  ];

  const mockSuppliers: Supplier[] = [
    {
      id: '1',
      name: 'شركة التوريدات المتقدمة',
      phone: '0112345678',
      email: '<EMAIL>',
      address: 'الدمام، المملكة العربية السعودية',
      balance: 2000,
      totalPurchases: 15000,
      createdAt: new Date()
    }
  ];

  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'لابتوب ديل',
      nameAr: 'لابتوب ديل',
      category: 'إلكترونيات',
      brand: 'Dell',
      barcode: '123456789',
      unit: 'قطعة',
      purchasePrice: 2000,
      salePrice: 2500,
      stock: 10,
      minStock: 2,
      description: 'لابتوب ديل عالي الأداء',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '2',
      name: 'ماوس لاسلكي',
      nameAr: 'ماوس لاسلكي',
      category: 'إكسسوارات',
      brand: 'Logitech',
      barcode: '987654321',
      unit: 'قطعة',
      purchasePrice: 50,
      salePrice: 75,
      stock: 25,
      minStock: 5,
      description: 'ماوس لاسلكي عالي الدقة',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  const mockInvoices: Invoice[] = [
    {
      id: '1',
      invoice_number: 'INV-001',
      type: 'sales',
      customer_id: '1',
      customer: mockCustomers[0],
      invoice_date: new Date().toISOString().split('T')[0],
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      total_amount: 2575,
      paid_amount: 2575,
      remaining_amount: 0,
      payment_status: 'paid',
      payment_method: 'cash',
      discount_amount: 0,
      discount_percent: 0,
      tax_amount: 75,
      tax_percent: 3,
      notes: 'فاتورة بيع لابتوب',
      items: [
        {
          id: '1',
          invoice_id: '1',
          product_id: '1',
          product_name: 'لابتوب ديل',
          quantity: 1,
          unit_price: 2500,
          discount_amount: 0,
          discount_percent: 0,
          tax_amount: 75,
          tax_percent: 3,
          total_price: 2575,
          notes: ''
        }
      ],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ];

  const mockSuspendedInvoices: SuspendedInvoice[] = [];

  // معالجات الأحداث
  const handleCreateInvoice = async (invoiceData: any) => {
    try {
      console.log('Creating invoice:', invoiceData);
      toast({
        title: "تم الإنشاء",
        description: "تم إنشاء الفاتورة بنجاح (وضع التجربة)"
      });
    } catch (error) {
      console.error('Error creating invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في إنشاء الفاتورة",
        variant: "destructive"
      });
    }
  };

  const handleUpdateInvoice = async (id: string, invoiceData: any) => {
    try {
      console.log('Updating invoice:', id, invoiceData);
      toast({
        title: "تم التحديث",
        description: "تم تحديث الفاتورة بنجاح (وضع التجربة)"
      });
    } catch (error) {
      console.error('Error updating invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحديث الفاتورة",
        variant: "destructive"
      });
    }
  };

  const handleDeleteInvoice = async (id: string) => {
    try {
      console.log('Deleting invoice:', id);
      toast({
        title: "تم الحذف",
        description: "تم حذف الفاتورة بنجاح (وضع التجربة)"
      });
    } catch (error) {
      console.error('Error deleting invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في حذف الفاتورة",
        variant: "destructive"
      });
    }
  };

  const handleSuspendInvoice = async (invoiceData: any) => {
    try {
      console.log('Suspending invoice:', invoiceData);
      toast({
        title: "تم التعليق",
        description: "تم تعليق الفاتورة بنجاح (وضع التجربة)"
      });
    } catch (error) {
      console.error('Error suspending invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في تعليق الفاتورة",
        variant: "destructive"
      });
    }
  };

  const handleRestoreSuspendedInvoice = async (suspendedInvoice: SuspendedInvoice) => {
    try {
      console.log('Restoring suspended invoice:', suspendedInvoice);
      toast({
        title: "تم الاستعادة",
        description: "تم استعادة الفاتورة المعلقة بنجاح (وضع التجربة)"
      });
    } catch (error) {
      console.error('Error restoring suspended invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في استعادة الفاتورة المعلقة",
        variant: "destructive"
      });
    }
  };

  const handleDeleteSuspendedInvoice = async (id: string) => {
    try {
      console.log('Deleting suspended invoice:', id);
      toast({
        title: "تم الحذف",
        description: "تم حذف الفاتورة المعلقة نهائياً (وضع التجربة)"
      });
    } catch (error) {
      console.error('Error deleting suspended invoice:', error);
      toast({
        title: "خطأ",
        description: "فشل في حذف الفاتورة المعلقة",
        variant: "destructive"
      });
    }
  };

  const handleSearchInvoice = async (criteria: any) => {
    console.log('Searching invoices:', criteria);
    return mockInvoices.filter(invoice => {
      if (criteria.invoice_number) {
        return invoice.invoice_number.includes(criteria.invoice_number);
      }
      return true;
    });
  };

  const handleDownloadPDF = async (invoice: Invoice) => {
    try {
      console.log('Downloading PDF for invoice:', invoice.invoice_number);
      toast({
        title: "تم التحميل",
        description: "تم تحميل ملف PDF بنجاح (وضع التجربة)"
      });
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل ملف PDF",
        variant: "destructive"
      });
    }
  };

  // حساب الإحصائيات
  const stats = {
    totalInvoices: mockInvoices.length,
    totalSales: mockInvoices.filter(i => i.type === 'sales').reduce((sum, i) => sum + i.total_amount, 0),
    totalPurchases: mockInvoices.filter(i => i.type === 'purchase').reduce((sum, i) => sum + i.total_amount, 0),
    pendingAmount: mockInvoices.reduce((sum, i) => sum + i.remaining_amount, 0)
  };

  return (
    <Layout>
      <div className="space-y-6" dir="rtl">
        {/* رأس الصفحة */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">نظام إدارة الفواتير المتقدم - وضع التجربة</h1>
            <p className="text-gray-600 mt-1">إدارة شاملة لجميع أنواع الفواتير والمدفوعات (بيانات وهمية للاختبار)</p>
          </div>
        </div>

        {/* الإحصائيات السريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-600 text-sm font-medium">إجمالي الفواتير</p>
                  <p className="text-2xl font-bold text-blue-900">{stats.totalInvoices}</p>
                </div>
                <FileText className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-600 text-sm font-medium">إجمالي المبيعات</p>
                  <p className="text-2xl font-bold text-green-900">{formatCurrency(stats.totalSales)}</p>
                </div>
                <ShoppingCart className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-600 text-sm font-medium">إجمالي المشتريات</p>
                  <p className="text-2xl font-bold text-orange-900">{formatCurrency(stats.totalPurchases)}</p>
                </div>
                <Package className="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-600 text-sm font-medium">المبالغ المعلقة</p>
                  <p className="text-2xl font-bold text-red-900">{formatCurrency(stats.pendingAmount)}</p>
                </div>
                <RotateCcw className="w-8 h-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* التبويبات الرئيسية */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="invoices" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              إدارة الفواتير
            </TabsTrigger>
            <TabsTrigger value="search" className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              البحث الذكي
            </TabsTrigger>
            <TabsTrigger value="reports" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              التقارير
            </TabsTrigger>
            <TabsTrigger value="suspended" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              الفواتير المعلقة
            </TabsTrigger>
          </TabsList>

          {/* تبويب إدارة الفواتير */}
          <TabsContent value="invoices">
            <InvoiceManager
              invoices={searchResults.length > 0 ? searchResults : mockInvoices}
              customers={mockCustomers}
              suppliers={mockSuppliers}
              products={mockProducts}
              onCreateInvoice={handleCreateInvoice}
              onUpdateInvoice={handleUpdateInvoice}
              onDeleteInvoice={handleDeleteInvoice}
              onSuspendInvoice={handleSuspendInvoice}
              onDownloadPDF={handleDownloadPDF}
              onSearchInvoice={handleSearchInvoice}
            />
          </TabsContent>

          {/* تبويب البحث الذكي */}
          <TabsContent value="search">
            <SmartSearch
              invoices={mockInvoices}
              customers={mockCustomers}
              suppliers={mockSuppliers}
              products={mockProducts}
              onSearchResults={setSearchResults}
            />
          </TabsContent>

          {/* تبويب التقارير */}
          <TabsContent value="reports">
            <IntelligentReports
              invoices={mockInvoices}
              customers={mockCustomers}
              suppliers={mockSuppliers}
              products={mockProducts}
            />
          </TabsContent>

          {/* تبويب الفواتير المعلقة */}
          <TabsContent value="suspended">
            <SuspendedInvoices
              suspendedInvoices={mockSuspendedInvoices}
              customers={mockCustomers}
              suppliers={mockSuppliers}
              onRestoreInvoice={handleRestoreSuspendedInvoice}
              onDeleteSuspendedInvoice={handleDeleteSuspendedInvoice}
              onViewInvoiceData={(data) => console.log('View invoice data:', data)}
            />
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default InvoicesDemo;
