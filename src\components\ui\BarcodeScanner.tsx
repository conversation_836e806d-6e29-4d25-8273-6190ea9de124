import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { 
  Scan, 
  Camera, 
  X, 
  Check, 
  AlertTriangle,
  Keyboard,
  Search
} from 'lucide-react';
import { Product } from '@/types';
import { toast } from '@/hooks/use-toast';

interface BarcodeScannerProps {
  products: Product[];
  onProductFound: (product: Product) => void;
  onBarcodeScanned?: (barcode: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

const BarcodeScanner: React.FC<BarcodeScannerProps> = ({
  products,
  onProductFound,
  onBarcodeScanned,
  placeholder = "امسح الباركود أو أدخله يدوياً...",
  disabled = false
}) => {
  const [isScanning, setIsScanning] = useState(false);
  const [manualBarcode, setManualBarcode] = useState('');
  const [lastScannedBarcode, setLastScannedBarcode] = useState('');
  const [scanHistory, setScanHistory] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // تنظيف الكاميرا عند إلغاء التحميل
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);

  // البحث عن المنتج بالباركود
  const findProductByBarcode = (barcode: string): Product | null => {
    return products.find(product => 
      product.barcode === barcode || 
      product.sku === barcode ||
      product.id === barcode
    ) || null;
  };

  // معالجة الباركود الممسوح أو المدخل
  const handleBarcodeInput = (barcode: string) => {
    if (!barcode.trim()) return;

    const cleanBarcode = barcode.trim();
    setLastScannedBarcode(cleanBarcode);
    
    // إضافة إلى التاريخ
    setScanHistory(prev => {
      const newHistory = [cleanBarcode, ...prev.filter(b => b !== cleanBarcode)];
      return newHistory.slice(0, 10); // الاحتفاظ بآخر 10 عمليات مسح
    });

    // البحث عن المنتج
    const product = findProductByBarcode(cleanBarcode);
    
    if (product) {
      onProductFound(product);
      toast({
        title: "تم العثور على المنتج",
        description: `${product.name} - ${product.barcode}`,
      });
    } else {
      toast({
        title: "لم يتم العثور على المنتج",
        description: `الباركود: ${cleanBarcode}`,
        variant: "destructive"
      });
    }

    // استدعاء callback إضافي إذا تم تمريره
    onBarcodeScanned?.(cleanBarcode);
    
    // مسح الحقل
    setManualBarcode('');
  };

  // بدء تشغيل الكاميرا
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // الكاميرا الخلفية
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });
      
      streamRef.current = stream;
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
      setIsScanning(true);
    } catch (error) {
      console.error('خطأ في تشغيل الكاميرا:', error);
      toast({
        title: "خطأ في الكاميرا",
        description: "لا يمكن الوصول إلى الكاميرا. تأكد من منح الصلاحيات.",
        variant: "destructive"
      });
    }
  };

  // إيقاف الكاميرا
  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsScanning(false);
  };

  // معالجة الإدخال اليدوي
  const handleManualInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleBarcodeInput(manualBarcode);
    }
  };

  // معالجة اللصق
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text');
    setManualBarcode(pastedText);
    setTimeout(() => handleBarcodeInput(pastedText), 100);
  };

  return (
    <div className="space-y-4" dir="rtl">
      {/* حقل الإدخال الرئيسي */}
      <div className="relative">
        <Label>الباركود</Label>
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Scan className="absolute right-3 top-3 w-4 h-4 text-gray-400" />
            <Input
              ref={inputRef}
              value={manualBarcode}
              onChange={(e) => setManualBarcode(e.target.value)}
              onKeyDown={handleManualInput}
              onPaste={handlePaste}
              placeholder={placeholder}
              disabled={disabled}
              className="pr-10"
              autoComplete="off"
            />
          </div>
          
          <Button
            onClick={() => handleBarcodeInput(manualBarcode)}
            disabled={disabled || !manualBarcode.trim()}
            variant="outline"
          >
            <Search className="w-4 h-4" />
          </Button>

          {/* زر فتح الكاميرا */}
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" disabled={disabled}>
                <Camera className="w-4 h-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>مسح الباركود</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                {!isScanning ? (
                  <div className="text-center space-y-4">
                    <div className="p-8 border-2 border-dashed border-gray-300 rounded-lg">
                      <Camera className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                      <p className="text-gray-600">اضغط لبدء مسح الباركود</p>
                    </div>
                    <Button onClick={startCamera} className="w-full">
                      <Camera className="w-4 h-4 ml-2" />
                      تشغيل الكاميرا
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="relative">
                      <video
                        ref={videoRef}
                        autoPlay
                        playsInline
                        className="w-full rounded-lg"
                        style={{ maxHeight: '300px' }}
                      />
                      <div className="absolute inset-0 border-2 border-red-500 rounded-lg pointer-events-none">
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-24 border-2 border-red-500 rounded"></div>
                      </div>
                    </div>
                    
                    <div className="text-center text-sm text-gray-600">
                      وجه الكاميرا نحو الباركود
                    </div>
                    
                    <Button onClick={stopCamera} variant="destructive" className="w-full">
                      <X className="w-4 h-4 ml-2" />
                      إيقاف المسح
                    </Button>
                  </div>
                )}

                {/* الإدخال اليدوي كبديل */}
                <div className="border-t pt-4">
                  <Label>أو أدخل الباركود يدوياً</Label>
                  <div className="flex gap-2 mt-2">
                    <Input
                      value={manualBarcode}
                      onChange={(e) => setManualBarcode(e.target.value)}
                      onKeyDown={handleManualInput}
                      placeholder="أدخل الباركود..."
                      className="flex-1"
                    />
                    <Button
                      onClick={() => handleBarcodeInput(manualBarcode)}
                      disabled={!manualBarcode.trim()}
                    >
                      <Check className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* آخر باركود ممسوح */}
      {lastScannedBarcode && (
        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-blue-600" />
              <span className="text-sm text-blue-800">آخر مسح:</span>
              <Badge variant="outline">{lastScannedBarcode}</Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setLastScannedBarcode('')}
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        </div>
      )}

      {/* تاريخ المسح */}
      {scanHistory.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">تاريخ المسح</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {scanHistory.slice(0, 5).map((barcode, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <span className="font-mono">{barcode}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleBarcodeInput(barcode)}
                  >
                    <Search className="w-3 h-3" />
                  </Button>
                </div>
              ))}
              
              {scanHistory.length > 5 && (
                <div className="text-xs text-gray-500 text-center">
                  و {scanHistory.length - 5} عنصر آخر...
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* نصائح الاستخدام */}
      <Card className="bg-gray-50">
        <CardContent className="p-3">
          <div className="flex items-start gap-2">
            <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
            <div className="text-xs text-gray-600 space-y-1">
              <div><strong>نصائح:</strong></div>
              <div>• يمكنك مسح الباركود بالكاميرا أو إدخاله يدوياً</div>
              <div>• اضغط Enter بعد الإدخال اليدوي</div>
              <div>• يمكنك لصق الباركود من الحافظة</div>
              <div>• تأكد من وضوح الباركود عند المسح</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BarcodeScanner;
