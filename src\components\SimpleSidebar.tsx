import React, { useState } from 'react';
import {
  Home,
  Package,
  Archive,
  Users,
  Truck,
  FileText,
  BarChart3,
  Settings,
  ChevronRight,
  Search,
  Bell,
  Moon,
  Sun,
  ShoppingCart,
  CreditCard,
  PieChart,
  Shield,
  AlertTriangle
} from 'lucide-react';

export function SimpleSidebar() {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // دالة لترجمة أسماء الأقسام
  const getCategoryName = (category: string) => {
    const categoryNames = {
      'main': 'الرئيسية',
      'sales': 'المبيعات',
      'inventory': 'المخزون',
      'relations': 'العلاقات',
      'reports': 'التقارير',
      'admin': 'الإدارة'
    };
    return categoryNames[category as keyof typeof categoryNames] || category;
  };

  const menuItems = [
    // القسم الرئيسي
    {
      icon: Home,
      label: 'لوحة التحكم',
      href: '/dashboard-demo',
      badge: null,
      gradient: 'from-blue-500 to-purple-600',
      category: 'main'
    },

    // قسم المبيعات والعمليات
    {
      icon: ShoppingCart,
      label: 'نقاط البيع',
      href: '/pos',
      badge: 'جديد',
      gradient: 'from-emerald-500 to-green-600',
      category: 'sales'
    },
    {
      icon: FileText,
      label: 'الفواتير',
      href: '/invoices',
      badge: '12',
      gradient: 'from-blue-500 to-cyan-600',
      category: 'sales'
    },
    {
      icon: CreditCard,
      label: 'المدفوعات',
      href: '/payments',
      badge: null,
      gradient: 'from-purple-500 to-pink-600',
      category: 'sales'
    },

    // قسم المخزون والمنتجات
    {
      icon: Package,
      label: 'المنتجات',
      href: '/products',
      badge: '245',
      gradient: 'from-orange-500 to-red-600',
      category: 'inventory'
    },
    {
      icon: Archive,
      label: 'المخزون',
      href: '/inventory',
      badge: '8',
      gradient: 'from-yellow-500 to-orange-600',
      category: 'inventory'
    },
    {
      icon: AlertTriangle,
      label: 'تنبيهات المخزون',
      href: '/stock-alerts',
      badge: '3',
      gradient: 'from-red-500 to-pink-600',
      category: 'inventory'
    },

    // قسم العلاقات
    {
      icon: Users,
      label: 'العملاء',
      href: '/customers',
      badge: '89',
      gradient: 'from-indigo-500 to-purple-600',
      category: 'relations'
    },
    {
      icon: Truck,
      label: 'الموردين',
      href: '/suppliers',
      badge: '15',
      gradient: 'from-teal-500 to-cyan-600',
      category: 'relations'
    },

    // قسم التقارير والتحليلات
    {
      icon: BarChart3,
      label: 'التحليلات',
      href: '/analytics',
      badge: null,
      gradient: 'from-green-500 to-emerald-600',
      category: 'reports'
    },
    {
      icon: PieChart,
      label: 'التقارير المالية',
      href: '/financial-reports',
      badge: null,
      gradient: 'from-violet-500 to-purple-600',
      category: 'reports'
    },

    // قسم الإعدادات والإدارة
    {
      icon: Settings,
      label: 'الإعدادات',
      href: '/settings',
      badge: null,
      gradient: 'from-gray-500 to-slate-600',
      category: 'admin'
    },
    {
      icon: Shield,
      label: 'الصلاحيات',
      href: '/permissions',
      badge: null,
      gradient: 'from-amber-500 to-yellow-600',
      category: 'admin'
    }
  ];

  const filteredItems = menuItems.filter(item =>
    item.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      right: 0,
      width: '260px',
      height: '100vh',
      background: isDarkMode
        ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
        : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
      color: isDarkMode ? '#f1f5f9' : '#1e293b',
      zIndex: 1000,
      overflowY: 'auto',
      boxShadow: isDarkMode
        ? '-4px 0 20px rgba(0,0,0,0.3)'
        : '-4px 0 20px rgba(0,0,0,0.1)',
      backdropFilter: 'blur(10px)',
      borderLeft: isDarkMode
        ? '1px solid rgba(255,255,255,0.1)'
        : '1px solid rgba(0,0,0,0.1)'
    }}>
      {/* رأس الشريط الجانبي المحسن */}
      <div style={{
        padding: '1rem',
        borderBottom: isDarkMode
          ? '1px solid rgba(255,255,255,0.1)'
          : '1px solid rgba(0,0,0,0.1)',
        background: isDarkMode
          ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
          : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
        color: isDarkMode ? '#f1f5f9' : '#1e293b'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.75rem'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '20px',
            color: 'white',
            flexShrink: 0
          }}>
            🏪
          </div>
          <div>
            <h2 style={{
              fontSize: '1rem',
              fontWeight: '600',
              margin: '0 0 0.25rem 0',
              lineHeight: 1
            }}>
              متجر DZ
            </h2>
            <p style={{
              fontSize: '0.75rem',
              opacity: 0.7,
              margin: 0,
              lineHeight: 1
            }}>
              نظام إدارة
            </p>
          </div>
        </div>
      </div>

      {/* شريط البحث المحسن */}
      <div style={{ padding: '1rem' }}>
        <div style={{
          position: 'relative',
          marginBottom: '0.75rem'
        }}>
          <Search
            size={16}
            style={{
              position: 'absolute',
              right: '10px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: isDarkMode ? '#64748b' : '#94a3b8'
            }}
          />
          <input
            type="text"
            placeholder="البحث..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '8px 32px 8px 10px',
              border: isDarkMode
                ? '1px solid rgba(255,255,255,0.1)'
                : '1px solid rgba(0,0,0,0.1)',
              borderRadius: '8px',
              background: isDarkMode
                ? 'rgba(255,255,255,0.05)'
                : 'rgba(0,0,0,0.02)',
              color: isDarkMode ? '#f1f5f9' : '#1e293b',
              fontSize: '0.8rem',
              outline: 'none',
              transition: 'all 0.3s ease'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#3b82f6';
              e.target.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.1)';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = isDarkMode
                ? 'rgba(255,255,255,0.1)'
                : 'rgba(0,0,0,0.1)';
              e.target.style.boxShadow = 'none';
            }}
          />
        </div>

        {/* أزرار التحكم المحسنة */}
        <div style={{
          display: 'flex',
          gap: '0.5rem',
          marginBottom: '0.75rem'
        }}>
          <button
            onClick={() => setIsDarkMode(!isDarkMode)}
            style={{
              flex: 1,
              padding: '6px 8px',
              border: 'none',
              borderRadius: '6px',
              background: isDarkMode
                ? 'rgba(255,255,255,0.1)'
                : 'rgba(0,0,0,0.05)',
              color: isDarkMode ? '#f1f5f9' : '#1e293b',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.25rem',
              fontSize: '0.7rem'
            }}
          >
            {isDarkMode ? <Sun size={14} /> : <Moon size={14} />}
            {isDarkMode ? 'فاتح' : 'داكن'}
          </button>

          <button
            style={{
              padding: '6px 8px',
              border: 'none',
              borderRadius: '6px',
              background: isDarkMode
                ? 'rgba(255,255,255,0.1)'
                : 'rgba(0,0,0,0.05)',
              color: isDarkMode ? '#f1f5f9' : '#1e293b',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              position: 'relative'
            }}
          >
            <Bell size={14} />
            <span style={{
              position: 'absolute',
              top: '2px',
              right: '2px',
              width: '6px',
              height: '6px',
              background: '#ef4444',
              borderRadius: '50%'
            }} />
          </button>
        </div>
      </div>

      {/* قائمة التنقل المحسنة */}
      <nav style={{ padding: '0 1rem 1rem' }}>
        {filteredItems.map((item, index) => {
          const Icon = item.icon;
          const isActive = window.location.pathname === item.href;
          const prevItem = filteredItems[index - 1];
          const showSeparator = prevItem && prevItem.category !== item.category;

          return (
            <React.Fragment key={index}>
              {/* فاصل مبسط بين الأقسام */}
              {showSeparator && (
                <div style={{
                  margin: '0.5rem 0',
                  padding: '0.25rem 0.5rem',
                  fontSize: '0.65rem',
                  color: isDarkMode ? '#64748b' : '#94a3b8',
                  fontWeight: '600',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                  borderBottom: isDarkMode
                    ? '1px solid rgba(255,255,255,0.1)'
                    : '1px solid rgba(0,0,0,0.1)'
                }}>
                  {getCategoryName(item.category)}
                </div>
              )}

              <a
                href={item.href}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                padding: '0.5rem 0.75rem',
                margin: '0.25rem 0',
                color: isDarkMode ? '#f1f5f9' : '#1e293b',
                textDecoration: 'none',
                borderRadius: '8px',
                transition: 'all 0.2s ease',
                position: 'relative',
                background: isActive
                  ? `linear-gradient(135deg, ${item.gradient.split(' ')[1]} 0%, ${item.gradient.split(' ')[3]} 100%)`
                  : 'transparent',
                border: isActive
                  ? 'none'
                  : 'none',
                boxShadow: isActive
                  ? '0 2px 8px rgba(0,0,0,0.1)'
                  : 'none'
              }}
              onMouseEnter={(e) => {
                if (!isActive) {
                  e.currentTarget.style.background = isDarkMode
                    ? 'rgba(255,255,255,0.08)'
                    : 'rgba(0,0,0,0.04)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isActive) {
                  e.currentTarget.style.background = 'transparent';
                }
              }}
            >
              {/* خلفية متدرجة للعنصر النشط */}
              {isActive && (
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: `linear-gradient(135deg, ${item.gradient.split(' ')[1]} 0%, ${item.gradient.split(' ')[3]} 100%)`,
                  opacity: 0.9,
                  zIndex: -1
                }} />
              )}

              {/* أيقونة مبسطة */}
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: isActive
                  ? 'rgba(255,255,255,0.2)'
                  : `linear-gradient(135deg, ${item.gradient.split(' ')[1]} 0%, ${item.gradient.split(' ')[3]} 100%)`,
                color: 'white',
                flexShrink: 0
              }}>
                <Icon size={16} />
              </div>

              {/* النص */}
              <div style={{ flex: 1, minWidth: 0 }}>
                <span style={{
                  fontSize: '0.8rem',
                  fontWeight: isActive ? '600' : '500',
                  color: isActive ? 'white' : 'inherit',
                  lineHeight: 1.2
                }}>
                  {item.label}
                </span>
              </div>

              {/* الشارات */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.25rem'
              }}>
                {item.badge && (
                  <span style={{
                    background: isActive
                      ? 'rgba(255,255,255,0.25)'
                      : '#ef4444',
                    color: 'white',
                    fontSize: '0.65rem',
                    fontWeight: '600',
                    padding: '1px 6px',
                    borderRadius: '8px',
                    minWidth: '16px',
                    textAlign: 'center',
                    lineHeight: 1.2
                  }}>
                    {item.badge}
                  </span>
                )}

                <ChevronRight
                  size={14}
                  style={{
                    color: isActive ? 'rgba(255,255,255,0.6)' : isDarkMode ? '#64748b' : '#94a3b8',
                    opacity: 0.7
                  }}
                />
              </div>
            </a>
            </React.Fragment>
          );
        })}
      </nav>

      {/* معلومات المستخدم المحسنة */}
      <div style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        padding: '1rem',
        borderTop: isDarkMode
          ? '1px solid rgba(255,255,255,0.1)'
          : '1px solid rgba(0,0,0,0.1)',
        background: isDarkMode
          ? 'rgba(0,0,0,0.1)'
          : 'rgba(255,255,255,0.8)'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.75rem',
          padding: '0.75rem',
          borderRadius: '8px',
          background: isDarkMode
            ? 'rgba(255,255,255,0.05)'
            : 'rgba(0,0,0,0.02)',
          transition: 'all 0.2s ease',
          cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = isDarkMode
            ? 'rgba(255,255,255,0.08)'
            : 'rgba(0,0,0,0.04)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = isDarkMode
            ? 'rgba(255,255,255,0.05)'
            : 'rgba(0,0,0,0.02)';
        }}
        >
          <div style={{
            width: '36px',
            height: '36px',
            background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
            borderRadius: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '16px',
            color: 'white',
            flexShrink: 0
          }}>
            👨‍💼
          </div>
          <div style={{ flex: 1, minWidth: 0 }}>
            <p style={{
              fontSize: '0.8rem',
              fontWeight: '600',
              margin: '0 0 0.125rem 0',
              color: isDarkMode ? '#f1f5f9' : '#1e293b',
              lineHeight: 1.2
            }}>
              أحمد محمد
            </p>
            <p style={{
              fontSize: '0.7rem',
              opacity: 0.7,
              margin: 0,
              color: isDarkMode ? '#94a3b8' : '#64748b',
              lineHeight: 1.2
            }}>
              مدير النظام
            </p>
          </div>
          <div style={{
            width: '6px',
            height: '6px',
            background: '#10b981',
            borderRadius: '50%',
            flexShrink: 0
          }} />
        </div>

        <div style={{
          marginTop: '0.75rem',
          fontSize: '0.65rem',
          opacity: 0.5,
          textAlign: 'center',
          color: isDarkMode ? '#94a3b8' : '#64748b',
          lineHeight: 1.3
        }}>
          © 2024 متجر DZ • v3.0
        </div>
      </div>

      {/* إضافة الأنيميشن */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translate(0, 0) rotate(0deg); }
          33% { transform: translate(10px, -10px) rotate(1deg); }
          66% { transform: translate(-5px, 5px) rotate(-1deg); }
        }
      `}</style>
    </div>
  );
}
