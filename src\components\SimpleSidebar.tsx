import React, { useState } from 'react';
import {
  Home,
  Package,
  Archive,
  Users,
  Truck,
  FileText,
  BarChart3,
  Settings,
  ChevronRight,
  Moon,
  Sun,
  ShoppingCart,
  CreditCard,
  PieChart,
  Shield
} from 'lucide-react';

export function SimpleSidebar() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  // دالة لترجمة أسماء الأقسام
  const getCategoryName = (category: string) => {
    const categoryNames = {
      'main': 'الرئيسية',
      'sales': 'المبيعات',
      'inventory': 'المخزون',
      'relations': 'العلاقات',
      'reports': 'التقارير',
      'admin': 'الإدارة'
    };
    return categoryNames[category as keyof typeof categoryNames] || category;
  };

  const menuItems = [
    // القسم الرئيسي
    {
      icon: Home,
      label: 'لوحة التحكم',
      href: '/dashboard-demo',
      badge: null,
      gradient: 'from-blue-500 to-purple-600',
      category: 'main'
    },

    // قسم المبيعات والعمليات
    {
      icon: ShoppingCart,
      label: 'نقاط البيع',
      href: '/pos',
      badge: 'جديد',
      gradient: 'from-emerald-500 to-green-600',
      category: 'sales'
    },
    {
      icon: FileText,
      label: 'الفواتير',
      href: '/invoices',
      badge: '12',
      gradient: 'from-blue-500 to-cyan-600',
      category: 'sales'
    },
    {
      icon: CreditCard,
      label: 'المدفوعات',
      href: '/payments',
      badge: null,
      gradient: 'from-purple-500 to-pink-600',
      category: 'sales'
    },

    // قسم المخزون والمنتجات
    {
      icon: Package,
      label: 'المنتجات',
      href: '/products',
      badge: '245',
      gradient: 'from-orange-500 to-red-600',
      category: 'inventory'
    },
    {
      icon: Archive,
      label: 'المخزون',
      href: '/inventory',
      badge: '8',
      gradient: 'from-yellow-500 to-orange-600',
      category: 'inventory'
    },


    // قسم العلاقات
    {
      icon: Users,
      label: 'العملاء',
      href: '/customers',
      badge: '89',
      gradient: 'from-indigo-500 to-purple-600',
      category: 'relations'
    },
    {
      icon: Truck,
      label: 'الموردين',
      href: '/suppliers',
      badge: '15',
      gradient: 'from-teal-500 to-cyan-600',
      category: 'relations'
    },

    // قسم التقارير والتحليلات
    {
      icon: BarChart3,
      label: 'التحليلات',
      href: '/analytics',
      badge: null,
      gradient: 'from-green-500 to-emerald-600',
      category: 'reports'
    },
    {
      icon: PieChart,
      label: 'التقارير المالية',
      href: '/financial-reports',
      badge: null,
      gradient: 'from-violet-500 to-purple-600',
      category: 'reports'
    },

    // قسم الإعدادات والإدارة
    {
      icon: Settings,
      label: 'الإعدادات',
      href: '/settings',
      badge: null,
      gradient: 'from-gray-500 to-slate-600',
      category: 'admin'
    },
    {
      icon: Shield,
      label: 'الصلاحيات',
      href: '/permissions',
      badge: null,
      gradient: 'from-amber-500 to-yellow-600',
      category: 'admin'
    }
  ];

  const filteredItems = menuItems;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      right: 0,
      width: '260px',
      height: '100vh',
      background: isDarkMode
        ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
        : 'linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%)',
      color: isDarkMode ? '#f1f5f9' : '#1e293b',
      zIndex: 1000,
      overflowY: 'auto',
      boxShadow: isDarkMode
        ? '-4px 0 20px rgba(0,0,0,0.3)'
        : '-4px 0 20px rgba(0,0,0,0.1)',
      backdropFilter: 'blur(10px)',
      borderLeft: isDarkMode
        ? '1px solid rgba(255,255,255,0.1)'
        : '1px solid rgba(0,0,0,0.1)'
    }}>
      {/* رأس الشريط الجانبي المحسن */}
      <div style={{
        padding: '1rem',
        borderBottom: isDarkMode
          ? '1px solid rgba(255,255,255,0.1)'
          : '1px solid rgba(0,0,0,0.1)',
        background: isDarkMode
          ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
          : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
        color: isDarkMode ? '#f1f5f9' : '#1e293b'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.75rem'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '20px',
            color: 'white',
            flexShrink: 0
          }}>
            🏪
          </div>
          <div>
            <h2 style={{
              fontSize: '1rem',
              fontWeight: '600',
              margin: '0 0 0.25rem 0',
              lineHeight: 1
            }}>
              متجر DZ
            </h2>
            <p style={{
              fontSize: '0.75rem',
              opacity: 0.7,
              margin: 0,
              lineHeight: 1
            }}>
              نظام إدارة
            </p>
          </div>
        </div>
      </div>

      {/* زر التحكم في الوضع فقط */}
      <div style={{ padding: '1rem' }}>
        <button
          onClick={() => setIsDarkMode(!isDarkMode)}
          style={{
            width: '100%',
            padding: '8px 12px',
            border: 'none',
            borderRadius: '8px',
            background: isDarkMode
              ? 'rgba(255,255,255,0.1)'
              : 'rgba(59, 130, 246, 0.1)',
            color: isDarkMode ? '#f1f5f9' : '#1e293b',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.5rem',
            fontSize: '0.8rem',
            fontWeight: '500',
            marginBottom: '0.75rem'
          }}
        >
          {isDarkMode ? <Sun size={16} /> : <Moon size={16} />}
          {isDarkMode ? 'الوضع الفاتح' : 'الوضع الداكن'}
        </button>
      </div>

      {/* قائمة التنقل المحسنة */}
      <nav style={{ padding: '0 1rem', paddingBottom: '20px' }}>
        {filteredItems.map((item, index) => {
          const Icon = item.icon;
          const isActive = window.location.pathname === item.href;
          const prevItem = filteredItems[index - 1];
          const showSeparator = prevItem && prevItem.category !== item.category;

          return (
            <React.Fragment key={index}>
              {/* فاصل مبسط بين الأقسام */}
              {showSeparator && (
                <div style={{
                  margin: '0.5rem 0',
                  padding: '0.25rem 0.5rem',
                  fontSize: '0.65rem',
                  color: isDarkMode ? '#64748b' : '#475569',
                  fontWeight: '600',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                  borderBottom: isDarkMode
                    ? '1px solid rgba(255,255,255,0.1)'
                    : '1px solid rgba(59, 130, 246, 0.2)'
                }}>
                  {getCategoryName(item.category)}
                </div>
              )}

              <a
                href={item.href}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                padding: '0.5rem 0.75rem',
                margin: '0.25rem 0',
                color: isDarkMode ? '#f1f5f9' : '#0f172a',
                textDecoration: 'none',
                borderRadius: '8px',
                transition: 'all 0.2s ease',
                position: 'relative',
                background: isActive
                  ? `linear-gradient(135deg, ${item.gradient.split(' ')[1]} 0%, ${item.gradient.split(' ')[3]} 100%)`
                  : isDarkMode
                    ? 'transparent'
                    : '#ffffff',
                border: isActive
                  ? 'none'
                  : isDarkMode
                    ? 'none'
                    : '1px solid rgba(226, 232, 240, 0.8)',
                boxShadow: isActive
                  ? isDarkMode
                    ? '0 2px 8px rgba(0,0,0,0.1)'
                    : '0 4px 16px rgba(0,0,0,0.12)'
                  : isDarkMode
                    ? 'none'
                    : '0 1px 3px rgba(0,0,0,0.05)'
              }}
              onMouseEnter={(e) => {
                if (!isActive) {
                  e.currentTarget.style.background = isDarkMode
                    ? 'rgba(255,255,255,0.08)'
                    : '#f8fafc';
                  e.currentTarget.style.boxShadow = isDarkMode
                    ? 'none'
                    : '0 2px 8px rgba(0,0,0,0.08)';
                  e.currentTarget.style.transform = 'translateY(-1px)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isActive) {
                  e.currentTarget.style.background = isDarkMode
                    ? 'transparent'
                    : '#ffffff';
                  e.currentTarget.style.boxShadow = isDarkMode
                    ? 'none'
                    : '0 1px 3px rgba(0,0,0,0.05)';
                  e.currentTarget.style.transform = 'translateY(0)';
                }
              }}
            >
              {/* خلفية متدرجة للعنصر النشط */}
              {isActive && (
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: `linear-gradient(135deg, ${item.gradient.split(' ')[1]} 0%, ${item.gradient.split(' ')[3]} 100%)`,
                  opacity: 0.9,
                  zIndex: -1
                }} />
              )}

              {/* أيقونة واضحة */}
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: isActive
                  ? `linear-gradient(135deg, ${item.gradient.split(' ')[1]} 0%, ${item.gradient.split(' ')[3]} 100%)`
                  : isDarkMode
                    ? `linear-gradient(135deg, ${item.gradient.split(' ')[1]} 0%, ${item.gradient.split(' ')[3]} 100%)`
                    : '#1e293b',
                color: 'white',
                flexShrink: 0,
                boxShadow: isDarkMode
                  ? 'none'
                  : '0 2px 8px rgba(30, 41, 59, 0.3)',
                border: isDarkMode
                  ? 'none'
                  : '1px solid rgba(30, 41, 59, 0.1)'
              }}>
                <Icon size={16} />
              </div>

              {/* النص */}
              <div style={{ flex: 1, minWidth: 0 }}>
                <span style={{
                  fontSize: '0.8rem',
                  fontWeight: isActive ? '600' : '500',
                  color: isActive
                    ? 'white'
                    : isDarkMode
                      ? 'inherit'
                      : '#1e293b',
                  lineHeight: 1.2,
                  textShadow: isActive && !isDarkMode ? '0 1px 2px rgba(0,0,0,0.1)' : 'none'
                }}>
                  {item.label}
                </span>
              </div>

              {/* الشارات */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.25rem'
              }}>
                {item.badge && (
                  <span style={{
                    background: isActive
                      ? 'rgba(255,255,255,0.25)'
                      : isDarkMode
                        ? '#ef4444'
                        : '#dc2626',
                    color: 'white',
                    fontSize: '0.65rem',
                    fontWeight: '600',
                    padding: '2px 6px',
                    borderRadius: '8px',
                    minWidth: '16px',
                    textAlign: 'center',
                    lineHeight: 1.2,
                    boxShadow: isDarkMode
                      ? 'none'
                      : '0 1px 3px rgba(0,0,0,0.2)'
                  }}>
                    {item.badge}
                  </span>
                )}

                <ChevronRight
                  size={14}
                  style={{
                    color: isActive
                      ? 'rgba(255,255,255,0.8)'
                      : isDarkMode
                        ? '#64748b'
                        : '#475569',
                    opacity: isActive ? 1 : 0.7
                  }}
                />
              </div>
            </a>
            </React.Fragment>
          );
        })}

        {/* معلومات الإصدار والمطور */}
        <div style={{
          margin: '1rem 0',
          padding: '0.75rem',
          textAlign: 'center',
          fontSize: '0.7rem',
          opacity: 0.6,
          color: isDarkMode ? '#94a3b8' : '#475569',
          background: isDarkMode
            ? 'rgba(255,255,255,0.03)'
            : 'rgba(59, 130, 246, 0.03)',
          borderRadius: '8px',
          border: isDarkMode
            ? '1px solid rgba(255,255,255,0.05)'
            : '1px solid rgba(59, 130, 246, 0.1)',
          lineHeight: 1.4
        }}>
          © 2024 متجر DZ • v3.0<br />
          مطور بـ ❤️ في الجزائر
        </div>
      </nav>



      {/* إضافة الأنيميشن */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translate(0, 0) rotate(0deg); }
          33% { transform: translate(10px, -10px) rotate(1deg); }
          66% { transform: translate(-5px, 5px) rotate(-1deg); }
        }
      `}</style>
    </div>
  );
}
