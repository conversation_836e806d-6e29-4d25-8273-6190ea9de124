import React, { useState } from 'react';
import {
  Home,
  Package,
  Users,
  Truck,
  Zap,
  FileText,
  BarChart3,
  Settings,
  ChevronRight,
  Search,
  Bell,
  Moon,
  Sun,
  ShoppingCart,
  CreditCard,
  TrendingDown,
  PieChart,
  Shield,
  AlertTriangle,
  Archive
} from 'lucide-react';

export function SimpleSidebar() {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // دالة لترجمة أسماء الأقسام
  const getCategoryName = (category: string) => {
    const categoryNames = {
      'main': '🏠 الرئيسية',
      'sales': '💰 المبيعات',
      'inventory': '📦 المخزون',
      'relations': '👥 العلاقات',
      'reports': '📊 التقارير',
      'admin': '⚙️ الإدارة'
    };
    return categoryNames[category as keyof typeof categoryNames] || category;
  };

  const menuItems = [
    // القسم الرئيسي
    {
      icon: Home,
      label: 'لوحة التحكم',
      href: '/dashboard-demo',
      badge: null,
      gradient: 'from-blue-500 to-purple-600',
      category: 'main'
    },

    // قسم المبيعات والعمليات
    {
      icon: ShoppingCart,
      label: 'نقاط البيع',
      href: '/pos',
      badge: 'جديد',
      gradient: 'from-emerald-500 to-green-600',
      category: 'sales'
    },
    {
      icon: FileText,
      label: 'الفواتير',
      href: '/invoices',
      badge: '12',
      gradient: 'from-blue-500 to-cyan-600',
      category: 'sales'
    },
    {
      icon: CreditCard,
      label: 'المدفوعات',
      href: '/payments',
      badge: null,
      gradient: 'from-purple-500 to-pink-600',
      category: 'sales'
    },

    // قسم المخزون والمنتجات
    {
      icon: Package,
      label: 'المنتجات',
      href: '/products',
      badge: '245',
      gradient: 'from-orange-500 to-red-600',
      category: 'inventory'
    },
    {
      icon: Archive,
      label: 'المخزون',
      href: '/inventory',
      badge: '8',
      gradient: 'from-yellow-500 to-orange-600',
      category: 'inventory'
    },
    {
      icon: AlertTriangle,
      label: 'تنبيهات المخزون',
      href: '/stock-alerts',
      badge: '3',
      gradient: 'from-red-500 to-pink-600',
      category: 'inventory'
    },

    // قسم العلاقات
    {
      icon: Users,
      label: 'العملاء',
      href: '/customers',
      badge: '89',
      gradient: 'from-indigo-500 to-purple-600',
      category: 'relations'
    },
    {
      icon: Truck,
      label: 'الموردين',
      href: '/suppliers',
      badge: '15',
      gradient: 'from-teal-500 to-cyan-600',
      category: 'relations'
    },

    // قسم التقارير والتحليلات
    {
      icon: BarChart3,
      label: 'التحليلات',
      href: '/analytics',
      badge: null,
      gradient: 'from-green-500 to-emerald-600',
      category: 'reports'
    },
    {
      icon: PieChart,
      label: 'التقارير المالية',
      href: '/financial-reports',
      badge: null,
      gradient: 'from-violet-500 to-purple-600',
      category: 'reports'
    },

    // قسم الإعدادات والإدارة
    {
      icon: Settings,
      label: 'الإعدادات',
      href: '/settings',
      badge: null,
      gradient: 'from-gray-500 to-slate-600',
      category: 'admin'
    },
    {
      icon: Shield,
      label: 'الصلاحيات',
      href: '/permissions',
      badge: null,
      gradient: 'from-amber-500 to-yellow-600',
      category: 'admin'
    }
  ];

  const filteredItems = menuItems.filter(item =>
    item.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      right: 0,
      width: '280px',
      height: '100vh',
      background: isDarkMode
        ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
        : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
      color: isDarkMode ? '#f1f5f9' : '#1e293b',
      zIndex: 1000,
      overflowY: 'auto',
      boxShadow: isDarkMode
        ? '-4px 0 20px rgba(0,0,0,0.3)'
        : '-4px 0 20px rgba(0,0,0,0.1)',
      backdropFilter: 'blur(10px)',
      borderLeft: isDarkMode
        ? '1px solid rgba(255,255,255,0.1)'
        : '1px solid rgba(0,0,0,0.1)'
    }}>
      {/* رأس الشريط الجانبي العصري */}
      <div style={{
        padding: '2rem 1.5rem',
        borderBottom: isDarkMode
          ? '1px solid rgba(255,255,255,0.1)'
          : '1px solid rgba(0,0,0,0.1)',
        background: isDarkMode
          ? 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)'
          : 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
        color: 'white',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* خلفية متحركة */}
        <div style={{
          position: 'absolute',
          top: '-50%',
          right: '-50%',
          width: '200%',
          height: '200%',
          background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
          animation: 'float 6s ease-in-out infinite'
        }} />

        <div style={{ position: 'relative', zIndex: 1 }}>
          <div style={{
            width: '80px',
            height: '80px',
            background: 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)',
            borderRadius: '20px',
            margin: '0 auto 1rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '32px',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
          }}>
            🏪
          </div>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: '700',
            margin: '0 0 0.5rem 0',
            textAlign: 'center',
            textShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            متجر DZ
          </h2>
          <p style={{
            fontSize: '0.875rem',
            opacity: 0.9,
            margin: 0,
            textAlign: 'center'
          }}>
            نظام إدارة عصري
          </p>
        </div>
      </div>

      {/* شريط البحث العصري */}
      <div style={{ padding: '1.5rem' }}>
        <div style={{
          position: 'relative',
          marginBottom: '1rem'
        }}>
          <Search
            size={18}
            style={{
              position: 'absolute',
              right: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: isDarkMode ? '#64748b' : '#94a3b8'
            }}
          />
          <input
            type="text"
            placeholder="البحث في القائمة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '12px 40px 12px 12px',
              border: isDarkMode
                ? '1px solid rgba(255,255,255,0.1)'
                : '1px solid rgba(0,0,0,0.1)',
              borderRadius: '12px',
              background: isDarkMode
                ? 'rgba(255,255,255,0.05)'
                : 'rgba(0,0,0,0.02)',
              color: isDarkMode ? '#f1f5f9' : '#1e293b',
              fontSize: '0.875rem',
              outline: 'none',
              transition: 'all 0.3s ease',
              backdropFilter: 'blur(10px)'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#3b82f6';
              e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = isDarkMode
                ? 'rgba(255,255,255,0.1)'
                : 'rgba(0,0,0,0.1)';
              e.target.style.boxShadow = 'none';
            }}
          />
        </div>

        {/* أزرار التحكم */}
        <div style={{
          display: 'flex',
          gap: '0.5rem',
          marginBottom: '1rem'
        }}>
          <button
            onClick={() => setIsDarkMode(!isDarkMode)}
            style={{
              flex: 1,
              padding: '8px',
              border: 'none',
              borderRadius: '8px',
              background: isDarkMode
                ? 'rgba(255,255,255,0.1)'
                : 'rgba(0,0,0,0.05)',
              color: isDarkMode ? '#f1f5f9' : '#1e293b',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem'
            }}
          >
            {isDarkMode ? <Sun size={16} /> : <Moon size={16} />}
            <span style={{ fontSize: '0.75rem' }}>
              {isDarkMode ? 'فاتح' : 'داكن'}
            </span>
          </button>

          <button
            style={{
              padding: '8px',
              border: 'none',
              borderRadius: '8px',
              background: isDarkMode
                ? 'rgba(255,255,255,0.1)'
                : 'rgba(0,0,0,0.05)',
              color: isDarkMode ? '#f1f5f9' : '#1e293b',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              position: 'relative'
            }}
          >
            <Bell size={16} />
            <span style={{
              position: 'absolute',
              top: '2px',
              right: '2px',
              width: '8px',
              height: '8px',
              background: '#ef4444',
              borderRadius: '50%'
            }} />
          </button>
        </div>
      </div>

      {/* قائمة التنقل العصرية مع فواصل */}
      <nav style={{ padding: '0 1.5rem 1rem' }}>
        {filteredItems.map((item, index) => {
          const Icon = item.icon;
          const isActive = window.location.pathname === item.href;
          const prevItem = filteredItems[index - 1];
          const showSeparator = prevItem && prevItem.category !== item.category;

          return (
            <React.Fragment key={index}>
              {/* فاصل بين الأقسام */}
              {showSeparator && (
                <div style={{
                  margin: '1rem 0',
                  height: '1px',
                  background: isDarkMode
                    ? 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%)'
                    : 'linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.1) 50%, transparent 100%)',
                  position: 'relative'
                }}>
                  <div style={{
                    position: 'absolute',
                    top: '-8px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    background: isDarkMode ? '#1e293b' : '#ffffff',
                    padding: '0 1rem',
                    fontSize: '0.75rem',
                    color: isDarkMode ? '#64748b' : '#94a3b8',
                    fontWeight: '500'
                  }}>
                    {getCategoryName(item.category)}
                  </div>
                </div>
              )}

              <a
                href={item.href}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '1rem',
                padding: '1rem',
                margin: '0.5rem 0',
                color: isDarkMode ? '#f1f5f9' : '#1e293b',
                textDecoration: 'none',
                borderRadius: '16px',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                position: 'relative',
                overflow: 'hidden',
                background: isActive
                  ? `linear-gradient(135deg, ${item.gradient.split(' ')[1]} 0%, ${item.gradient.split(' ')[3]} 100%)`
                  : isDarkMode
                    ? 'rgba(255,255,255,0.05)'
                    : 'rgba(0,0,0,0.02)',
                border: isActive
                  ? 'none'
                  : isDarkMode
                    ? '1px solid rgba(255,255,255,0.1)'
                    : '1px solid rgba(0,0,0,0.05)',
                boxShadow: isActive
                  ? '0 8px 25px rgba(0,0,0,0.15)'
                  : '0 2px 8px rgba(0,0,0,0.05)',
                transform: isActive ? 'translateY(-2px)' : 'translateY(0)'
              }}
              onMouseEnter={(e) => {
                if (!isActive) {
                  e.currentTarget.style.background = isDarkMode
                    ? 'rgba(255,255,255,0.1)'
                    : 'rgba(0,0,0,0.05)';
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isActive) {
                  e.currentTarget.style.background = isDarkMode
                    ? 'rgba(255,255,255,0.05)'
                    : 'rgba(0,0,0,0.02)';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.05)';
                }
              }}
            >
              {/* خلفية متدرجة للعنصر النشط */}
              {isActive && (
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: `linear-gradient(135deg, ${item.gradient.split(' ')[1]} 0%, ${item.gradient.split(' ')[3]} 100%)`,
                  opacity: 0.9,
                  zIndex: -1
                }} />
              )}

              {/* أيقونة مع خلفية دائرية */}
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: isActive
                  ? 'rgba(255,255,255,0.2)'
                  : `linear-gradient(135deg, ${item.gradient.split(' ')[1]} 0%, ${item.gradient.split(' ')[3]} 100%)`,
                color: isActive ? 'white' : 'white',
                backdropFilter: 'blur(10px)',
                flexShrink: 0
              }}>
                <Icon size={20} />
              </div>

              {/* النص والشارة */}
              <div style={{ flex: 1, minWidth: 0 }}>
                <span style={{
                  fontSize: '0.875rem',
                  fontWeight: isActive ? '600' : '500',
                  color: isActive ? 'white' : 'inherit'
                }}>
                  {item.label}
                </span>
              </div>

              {/* الشارات والأسهم */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                {item.badge && (
                  <span style={{
                    background: isActive
                      ? 'rgba(255,255,255,0.2)'
                      : '#ef4444',
                    color: 'white',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    padding: '2px 8px',
                    borderRadius: '12px',
                    minWidth: '20px',
                    textAlign: 'center'
                  }}>
                    {item.badge}
                  </span>
                )}

                <ChevronRight
                  size={16}
                  style={{
                    color: isActive ? 'rgba(255,255,255,0.7)' : isDarkMode ? '#64748b' : '#94a3b8',
                    transition: 'transform 0.3s ease'
                  }}
                />
              </div>
            </a>
            </React.Fragment>
          );
        })}
      </nav>

      {/* معلومات المستخدم العصرية */}
      <div style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        padding: '1.5rem',
        borderTop: isDarkMode
          ? '1px solid rgba(255,255,255,0.1)'
          : '1px solid rgba(0,0,0,0.1)',
        background: isDarkMode
          ? 'linear-gradient(135deg, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.1) 100%)'
          : 'linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.4) 100%)',
        backdropFilter: 'blur(20px)'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '1rem',
          padding: '1rem',
          borderRadius: '16px',
          background: isDarkMode
            ? 'rgba(255,255,255,0.05)'
            : 'rgba(0,0,0,0.02)',
          border: isDarkMode
            ? '1px solid rgba(255,255,255,0.1)'
            : '1px solid rgba(0,0,0,0.05)',
          transition: 'all 0.3s ease',
          cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = isDarkMode
            ? 'rgba(255,255,255,0.1)'
            : 'rgba(0,0,0,0.05)';
          e.currentTarget.style.transform = 'translateY(-2px)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = isDarkMode
            ? 'rgba(255,255,255,0.05)'
            : 'rgba(0,0,0,0.02)';
          e.currentTarget.style.transform = 'translateY(0)';
        }}
        >
          <div style={{
            width: '50px',
            height: '50px',
            background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
            borderRadius: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '20px',
            color: 'white',
            boxShadow: '0 4px 15px rgba(59, 130, 246, 0.3)',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{
              position: 'absolute',
              top: '-50%',
              right: '-50%',
              width: '200%',
              height: '200%',
              background: 'radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%)',
              animation: 'float 4s ease-in-out infinite'
            }} />
            👨‍💼
          </div>
          <div style={{ flex: 1 }}>
            <p style={{
              fontSize: '0.875rem',
              fontWeight: '600',
              margin: '0 0 0.25rem 0',
              color: isDarkMode ? '#f1f5f9' : '#1e293b'
            }}>
              أحمد محمد
            </p>
            <p style={{
              fontSize: '0.75rem',
              opacity: 0.7,
              margin: 0,
              color: isDarkMode ? '#94a3b8' : '#64748b'
            }}>
              مدير النظام
            </p>
          </div>
          <div style={{
            width: '8px',
            height: '8px',
            background: '#10b981',
            borderRadius: '50%',
            boxShadow: '0 0 0 2px rgba(16, 185, 129, 0.3)'
          }} />
        </div>

        <div style={{
          marginTop: '1rem',
          fontSize: '0.75rem',
          opacity: 0.6,
          textAlign: 'center',
          color: isDarkMode ? '#94a3b8' : '#64748b'
        }}>
          © 2024 متجر DZ • الإصدار 3.0<br />
          <span style={{ fontSize: '0.7rem' }}>مطور بـ ❤️ في الجزائر</span>
        </div>
      </div>

      {/* إضافة الأنيميشن */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translate(0, 0) rotate(0deg); }
          33% { transform: translate(10px, -10px) rotate(1deg); }
          66% { transform: translate(-5px, 5px) rotate(-1deg); }
        }
      `}</style>
    </div>
  );
}
