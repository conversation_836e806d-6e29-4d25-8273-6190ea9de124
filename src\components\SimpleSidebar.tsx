import React from 'react';
import { 
  Home, 
  Package, 
  Users, 
  Truck, 
  Zap, 
  FileText, 
  BarChart3, 
  Settings 
} from 'lucide-react';

export function SimpleSidebar() {
  const menuItems = [
    { icon: Home, label: 'القائمة الرئيسية', href: '/dashboard' },
    { icon: Home, label: 'لوحة التحكم', href: '/dashboard' },
    { icon: BarChart3, label: 'لوحة التحكم المتقدمة', href: '/dashboard-advanced' },
    { icon: BarChart3, label: 'تجربة لوحة التحكم', href: '/dashboard-demo' },
    { icon: Package, label: 'إدارة المنتجات', href: '/products' },
    { icon: Users, label: 'إدارة العملاء', href: '/customers' },
    { icon: Truck, label: 'إدارة الموردين', href: '/suppliers' },
    { icon: Zap, label: 'نظام الفواتير المتقدم', href: '/invoices' },
    { icon: Zap, label: 'تجربة الفواتير المتقدمة', href: '/invoices-advanced' },
    { icon: FileText, label: 'المخزون', href: '/inventory' },
    { icon: Settings, label: 'الإعدادات', href: '/settings' }
  ];

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      right: 0,
      width: '320px',
      height: '100vh',
      backgroundColor: '#3B82F6',
      color: 'white',
      zIndex: 1000,
      overflowY: 'auto',
      boxShadow: '-2px 0 10px rgba(0,0,0,0.1)'
    }}>
      {/* رأس الشريط الجانبي */}
      <div style={{
        padding: '1.5rem',
        borderBottom: '1px solid rgba(255,255,255,0.1)',
        textAlign: 'center'
      }}>
        <div style={{
          width: '60px',
          height: '60px',
          backgroundColor: 'white',
          borderRadius: '50%',
          margin: '0 auto 1rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#3B82F6',
          fontSize: '24px',
          fontWeight: 'bold'
        }}>
          🏪
        </div>
        <h2 style={{
          fontSize: '1.25rem',
          fontWeight: 'bold',
          margin: '0 0 0.5rem 0'
        }}>
          نظام إدارة المتاجر
        </h2>
        <p style={{
          fontSize: '0.875rem',
          opacity: 0.8,
          margin: 0
        }}>
          الإصدار 2.0
        </p>
      </div>

      {/* قائمة التنقل */}
      <nav style={{ padding: '1rem 0' }}>
        {menuItems.map((item, index) => {
          const Icon = item.icon;
          return (
            <a
              key={index}
              href={item.href}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                padding: '0.75rem 1.5rem',
                color: 'white',
                textDecoration: 'none',
                transition: 'all 0.2s',
                borderRight: window.location.pathname === item.href ? '4px solid white' : '4px solid transparent',
                backgroundColor: window.location.pathname === item.href ? 'rgba(255,255,255,0.1)' : 'transparent'
              }}
              onMouseEnter={(e) => {
                if (window.location.pathname !== item.href) {
                  e.currentTarget.style.backgroundColor = 'rgba(255,255,255,0.05)';
                }
              }}
              onMouseLeave={(e) => {
                if (window.location.pathname !== item.href) {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}
            >
              <Icon size={20} />
              <span style={{ fontSize: '0.875rem' }}>{item.label}</span>
            </a>
          );
        })}
      </nav>

      {/* معلومات المستخدم */}
      <div style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        padding: '1rem 1.5rem',
        borderTop: '1px solid rgba(255,255,255,0.1)',
        backgroundColor: 'rgba(0,0,0,0.1)'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.75rem'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            backgroundColor: 'rgba(255,255,255,0.2)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            👤
          </div>
          <div>
            <p style={{
              fontSize: '0.875rem',
              fontWeight: '500',
              margin: 0
            }}>
              المدير العام
            </p>
            <p style={{
              fontSize: '0.75rem',
              opacity: 0.7,
              margin: 0
            }}>
              <EMAIL>
            </p>
          </div>
        </div>
        
        <div style={{
          marginTop: '1rem',
          fontSize: '0.75rem',
          opacity: 0.6,
          textAlign: 'center'
        }}>
          © 2024 نظام إدارة المتاجر<br />
          جميع الحقوق محفوظة
        </div>
      </div>
    </div>
  );
}
