-- Enhanced Invoice System Migration
-- إضافة الميزات الشاملة لنظام الفواتير

-- 1. تحديث جدول الفواتير لدعم الميزات الجديدة
ALTER TABLE invoices 
ADD COLUMN IF NOT EXISTS subtotal_amount NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS reference_invoice_id UUID REFERENCES invoices(id),
ADD COLUMN IF NOT EXISTS return_reason TEXT,
ADD COLUMN IF NOT EXISTS is_suspended BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS suspended_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS created_by UUID,
ADD COLUMN IF NOT EXISTS currency VARCHAR(3) DEFAULT 'DZD',
ADD COLUMN IF NOT EXISTS exchange_rate NUMERIC DEFAULT 1.0,
ADD COLUMN IF NOT EXISTS cash_amount NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS credit_amount NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS card_amount NUMERIC DEFAULT 0;

-- 2. تحديث قيود الفحص للأنواع الجديدة
ALTER TABLE invoices DROP CONSTRAINT IF EXISTS invoices_type_check;
ALTER TABLE invoices ADD CONSTRAINT invoices_type_check 
CHECK (type IN ('sales', 'purchase', 'sales_return', 'purchase_return'));

-- تحديث قيود طرق الدفع
ALTER TABLE invoices DROP CONSTRAINT IF EXISTS invoices_payment_method_check;
ALTER TABLE invoices ADD CONSTRAINT invoices_payment_method_check 
CHECK (payment_method IN ('cash', 'card', 'credit', 'mixed') OR payment_method IS NULL);

-- تحديث قيود حالة الدفع
ALTER TABLE invoices DROP CONSTRAINT IF EXISTS invoices_payment_status_check;
ALTER TABLE invoices ADD CONSTRAINT invoices_payment_status_check 
CHECK (payment_status IN ('paid', 'partial', 'unpaid'));

-- 3. إنشاء جدول معاملات الدفع المفصلة
CREATE TABLE IF NOT EXISTS payment_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
    payment_method VARCHAR(20) NOT NULL CHECK (payment_method IN ('cash', 'card', 'credit')),
    amount NUMERIC NOT NULL CHECK (amount > 0),
    reference_number TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID
);

-- 4. إنشاء جدول تعليق الفواتير
CREATE TABLE IF NOT EXISTS suspended_invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_data JSONB NOT NULL,
    customer_id UUID REFERENCES customers(id),
    supplier_id UUID REFERENCES suppliers(id),
    suspended_by UUID NOT NULL,
    suspended_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE
);

-- 5. إنشاء جدول الأرقام التسلسلية
CREATE TABLE IF NOT EXISTS invoice_sequences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_type VARCHAR(20) NOT NULL,
    year INTEGER NOT NULL,
    last_number INTEGER DEFAULT 0,
    prefix VARCHAR(10) DEFAULT '',
    suffix VARCHAR(10) DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(invoice_type, year)
);

-- إدراج الأرقام التسلسلية الافتراضية للسنة الحالية
INSERT INTO invoice_sequences (invoice_type, year, prefix) VALUES
('sales', EXTRACT(YEAR FROM NOW()), 'SAL-'),
('purchase', EXTRACT(YEAR FROM NOW()), 'PUR-'),
('sales_return', EXTRACT(YEAR FROM NOW()), 'SR-'),
('purchase_return', EXTRACT(YEAR FROM NOW()), 'PR-')
ON CONFLICT (invoice_type, year) DO NOTHING;

-- 6. إنشاء جدول الصلاحيات
CREATE TABLE IF NOT EXISTS user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إدراج الأدوار الافتراضية
INSERT INTO user_roles (name, name_ar, description, permissions) VALUES
('admin', 'مدير النظام', 'صلاحيات كاملة لجميع العمليات', '{"invoices": {"create": true, "read": true, "update": true, "delete": true, "print": true, "return": true}, "products": {"create": true, "read": true, "update": true, "delete": true}, "customers": {"create": true, "read": true, "update": true, "delete": true}, "suppliers": {"create": true, "read": true, "update": true, "delete": true}, "reports": {"view": true, "export": true}}'),
('cashier', 'أمين الصندوق', 'إنشاء وطباعة فواتير البيع', '{"invoices": {"create": true, "read": true, "update": false, "delete": false, "print": true, "return": true}, "products": {"create": false, "read": true, "update": false, "delete": false}, "customers": {"create": true, "read": true, "update": true, "delete": false}}'),
('accountant', 'محاسب', 'عرض التقارير وإدارة الحسابات', '{"invoices": {"create": false, "read": true, "update": true, "delete": false, "print": true, "return": false}, "reports": {"view": true, "export": true}, "customers": {"read": true, "update": true}, "suppliers": {"read": true, "update": true}}'),
('warehouse', 'أمين المخزن', 'إدارة المخزون وفواتير الشراء', '{"invoices": {"create": true, "read": true, "update": true, "delete": false, "print": true, "return": true}, "products": {"create": true, "read": true, "update": true, "delete": false}, "suppliers": {"create": true, "read": true, "update": true, "delete": false}}')
ON CONFLICT (name) DO NOTHING;

-- 7. إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    role_id UUID REFERENCES user_roles(id),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. تحديث جدول عناصر الفواتير
ALTER TABLE invoice_items 
ADD COLUMN IF NOT EXISTS discount_percent NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS tax_percent NUMERIC DEFAULT 0,
ADD COLUMN IF NOT EXISTS original_price NUMERIC,
ADD COLUMN IF NOT EXISTS notes TEXT;

-- 9. إنشاء جدول سجل العمليات
CREATE TABLE IF NOT EXISTS audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(50) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values JSONB,
    new_values JSONB,
    user_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. إنشاء الفهارس للأداء
CREATE INDEX IF NOT EXISTS idx_invoices_type ON invoices(type);
CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(invoice_date);
CREATE INDEX IF NOT EXISTS idx_invoices_customer ON invoices(customer_id);
CREATE INDEX IF NOT EXISTS idx_invoices_supplier ON invoices(supplier_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(payment_status);
CREATE INDEX IF NOT EXISTS idx_invoices_created_by ON invoices(created_by);
CREATE INDEX IF NOT EXISTS idx_invoice_items_product ON invoice_items(product_id);
CREATE INDEX IF NOT EXISTS idx_payment_details_invoice ON payment_details(invoice_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_table_record ON audit_log(table_name, record_id);

-- 11. إنشاء دالة لتوليد الرقم التسلسلي
CREATE OR REPLACE FUNCTION generate_invoice_number(invoice_type_param VARCHAR)
RETURNS TEXT AS $$
DECLARE
    current_year INTEGER := EXTRACT(YEAR FROM NOW());
    sequence_record RECORD;
    new_number INTEGER;
    invoice_number TEXT;
BEGIN
    -- الحصول على السجل الحالي أو إنشاؤه
    SELECT * INTO sequence_record 
    FROM invoice_sequences 
    WHERE invoice_type = invoice_type_param AND year = current_year;
    
    IF NOT FOUND THEN
        INSERT INTO invoice_sequences (invoice_type, year, last_number, prefix)
        VALUES (invoice_type_param, current_year, 1, 
                CASE 
                    WHEN invoice_type_param = 'sales' THEN 'SAL-'
                    WHEN invoice_type_param = 'purchase' THEN 'PUR-'
                    WHEN invoice_type_param = 'sales_return' THEN 'SR-'
                    WHEN invoice_type_param = 'purchase_return' THEN 'PR-'
                    ELSE 'INV-'
                END)
        RETURNING * INTO sequence_record;
        new_number := 1;
    ELSE
        new_number := sequence_record.last_number + 1;
        UPDATE invoice_sequences 
        SET last_number = new_number 
        WHERE id = sequence_record.id;
    END IF;
    
    -- تكوين رقم الفاتورة
    invoice_number := sequence_record.prefix || LPAD(new_number::TEXT, 4, '0') || 
                     COALESCE(sequence_record.suffix, '');
    
    RETURN invoice_number;
END;
$$ LANGUAGE plpgsql;

-- 12. إنشاء دالة لحساب المبالغ تلقائياً
CREATE OR REPLACE FUNCTION calculate_invoice_totals(invoice_id_param UUID)
RETURNS VOID AS $$
DECLARE
    subtotal NUMERIC := 0;
    total_discount NUMERIC := 0;
    total_tax NUMERIC := 0;
    final_total NUMERIC := 0;
BEGIN
    -- حساب المجموع الفرعي والخصم والضريبة
    SELECT
        COALESCE(SUM(quantity * unit_price), 0),
        COALESCE(SUM(discount_amount), 0),
        COALESCE(SUM(tax_amount), 0)
    INTO subtotal, total_discount, total_tax
    FROM invoice_items
    WHERE invoice_id = invoice_id_param;

    final_total := subtotal - total_discount + total_tax;

    -- تحديث الفاتورة
    UPDATE invoices
    SET
        subtotal_amount = subtotal,
        discount_amount = total_discount,
        tax_amount = total_tax,
        total_amount = final_total,
        remaining_amount = final_total - paid_amount,
        updated_at = NOW()
    WHERE id = invoice_id_param;
END;
$$ LANGUAGE plpgsql;

-- 13. إنشاء دالة لتحديث المخزون
CREATE OR REPLACE FUNCTION update_inventory_on_invoice(invoice_id_param UUID, operation VARCHAR)
RETURNS VOID AS $$
DECLARE
    invoice_record RECORD;
    item_record RECORD;
    quantity_change INTEGER;
BEGIN
    -- الحصول على معلومات الفاتورة
    SELECT type INTO invoice_record FROM invoices WHERE id = invoice_id_param;

    -- تحديث المخزون لكل عنصر
    FOR item_record IN
        SELECT product_id, quantity
        FROM invoice_items
        WHERE invoice_id = invoice_id_param
    LOOP
        -- تحديد اتجاه التغيير في الكمية
        CASE
            WHEN invoice_record.type = 'sales' AND operation = 'add' THEN
                quantity_change := -item_record.quantity;
            WHEN invoice_record.type = 'sales' AND operation = 'remove' THEN
                quantity_change := item_record.quantity;
            WHEN invoice_record.type = 'purchase' AND operation = 'add' THEN
                quantity_change := item_record.quantity;
            WHEN invoice_record.type = 'purchase' AND operation = 'remove' THEN
                quantity_change := -item_record.quantity;
            WHEN invoice_record.type = 'sales_return' AND operation = 'add' THEN
                quantity_change := item_record.quantity;
            WHEN invoice_record.type = 'sales_return' AND operation = 'remove' THEN
                quantity_change := -item_record.quantity;
            WHEN invoice_record.type = 'purchase_return' AND operation = 'add' THEN
                quantity_change := -item_record.quantity;
            WHEN invoice_record.type = 'purchase_return' AND operation = 'remove' THEN
                quantity_change := item_record.quantity;
            ELSE
                quantity_change := 0;
        END CASE;

        -- تحديث المخزون
        UPDATE products
        SET stock = stock + quantity_change,
            updated_at = NOW()
        WHERE id = item_record.product_id;

        -- تسجيل حركة المخزون
        INSERT INTO inventory_movements (
            product_id,
            movement_type,
            quantity,
            reference_type,
            reference_id,
            notes
        ) VALUES (
            item_record.product_id,
            CASE
                WHEN quantity_change > 0 THEN 'in'
                ELSE 'out'
            END,
            ABS(quantity_change),
            'invoice',
            invoice_id_param,
            'تحديث تلقائي من الفاتورة'
        );
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 14. إنشاء triggers للتحديث التلقائي
CREATE OR REPLACE FUNCTION trigger_calculate_invoice_totals()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM calculate_invoice_totals(
        CASE
            WHEN TG_OP = 'DELETE' THEN OLD.invoice_id
            ELSE NEW.invoice_id
        END
    );
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لحساب المجاميع عند تغيير عناصر الفاتورة
DROP TRIGGER IF EXISTS trigger_invoice_items_totals ON invoice_items;
CREATE TRIGGER trigger_invoice_items_totals
    AFTER INSERT OR UPDATE OR DELETE ON invoice_items
    FOR EACH ROW
    EXECUTE FUNCTION trigger_calculate_invoice_totals();

-- 15. إنشاء دالة للبحث الذكي
CREATE OR REPLACE FUNCTION search_invoices(
    search_term TEXT DEFAULT '',
    invoice_type_filter TEXT DEFAULT '',
    date_from DATE DEFAULT NULL,
    date_to DATE DEFAULT NULL,
    customer_id_filter UUID DEFAULT NULL,
    supplier_id_filter UUID DEFAULT NULL,
    payment_status_filter TEXT DEFAULT '',
    limit_count INTEGER DEFAULT 50,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    invoice_number TEXT,
    type TEXT,
    customer_name TEXT,
    supplier_name TEXT,
    total_amount NUMERIC,
    paid_amount NUMERIC,
    remaining_amount NUMERIC,
    payment_status TEXT,
    payment_method TEXT,
    invoice_date DATE,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        i.id,
        i.invoice_number,
        i.type,
        c.name as customer_name,
        s.name as supplier_name,
        i.total_amount,
        i.paid_amount,
        i.remaining_amount,
        i.payment_status,
        i.payment_method,
        i.invoice_date,
        i.created_at
    FROM invoices i
    LEFT JOIN customers c ON i.customer_id = c.id
    LEFT JOIN suppliers s ON i.supplier_id = s.id
    WHERE
        (search_term = '' OR
         i.invoice_number ILIKE '%' || search_term || '%' OR
         c.name ILIKE '%' || search_term || '%' OR
         s.name ILIKE '%' || search_term || '%' OR
         i.notes ILIKE '%' || search_term || '%')
        AND (invoice_type_filter = '' OR i.type = invoice_type_filter)
        AND (date_from IS NULL OR i.invoice_date >= date_from)
        AND (date_to IS NULL OR i.invoice_date <= date_to)
        AND (customer_id_filter IS NULL OR i.customer_id = customer_id_filter)
        AND (supplier_id_filter IS NULL OR i.supplier_id = supplier_id_filter)
        AND (payment_status_filter = '' OR i.payment_status = payment_status_filter)
    ORDER BY i.created_at DESC
    LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- 16. إنشاء views للتقارير
CREATE OR REPLACE VIEW invoice_summary AS
SELECT
    i.id,
    i.invoice_number,
    i.type,
    i.invoice_date,
    COALESCE(c.name, 'عميل عام') as customer_name,
    COALESCE(s.name, '') as supplier_name,
    i.total_amount,
    i.paid_amount,
    i.remaining_amount,
    i.payment_status,
    i.payment_method,
    i.currency,
    COUNT(ii.id) as items_count,
    i.created_at
FROM invoices i
LEFT JOIN customers c ON i.customer_id = c.id
LEFT JOIN suppliers s ON i.supplier_id = s.id
LEFT JOIN invoice_items ii ON i.id = ii.invoice_id
GROUP BY i.id, c.name, s.name;

-- 17. إنشاء view لتقرير المبيعات اليومية
CREATE OR REPLACE VIEW daily_sales_report AS
SELECT
    invoice_date,
    COUNT(*) as invoices_count,
    SUM(CASE WHEN type = 'sales' THEN total_amount ELSE 0 END) as sales_total,
    SUM(CASE WHEN type = 'sales_return' THEN total_amount ELSE 0 END) as returns_total,
    SUM(CASE WHEN type = 'sales' THEN total_amount ELSE 0 END) -
    SUM(CASE WHEN type = 'sales_return' THEN total_amount ELSE 0 END) as net_sales,
    SUM(CASE WHEN payment_status = 'paid' THEN paid_amount ELSE 0 END) as cash_collected
FROM invoices
WHERE type IN ('sales', 'sales_return')
GROUP BY invoice_date
ORDER BY invoice_date DESC;
