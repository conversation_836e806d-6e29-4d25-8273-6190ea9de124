import React from 'react';
import { Button } from '@/components/ui/button';
import { generateInvoicePDF } from '@/utils/pdf-generator';

// بيانات تجريبية للفاتورة
const testInvoice = {
  id: 'test-1',
  invoice_number: 'INV-0001',
  invoice_date: '2025-06-17',
  due_date: null,
  total_amount: 6250.00,
  paid_amount: 6000.00,
  remaining_amount: 250.00,
  payment_status: 'partial',
  payment_method: 'cash',
  notes: 'هذه فاتورة تجريبية لاختبار النصوص العربية والتاريخ الفرنسي',
  customers: {
    name: 'هشام تمام',
    phone: '0549876907',
    email: '<EMAIL>'
  },
  invoice_items: [
    {
      quantity: 10,
      unit_price: 500.00,
      total_price: 5000.00,
      products: {
        name: 'منتج تجريبي 1'
      }
    },
    {
      quantity: 5,
      unit_price: 250.00,
      total_price: 1250.00,
      products: {
        name: 'منتج تجريبي 2'
      }
    }
  ]
};

const PDFTest: React.FC = () => {
  const handleGeneratePDF = async () => {
    try {
      console.log('بدء إنشاء PDF...');
      const doc = await generateInvoicePDF(testInvoice);
      doc.save(`test-invoice-${testInvoice.invoice_number}.pdf`);
      console.log('تم إنشاء PDF بنجاح!');
    } catch (error) {
      console.error('خطأ في إنشاء PDF:', error);
      alert('حدث خطأ في إنشاء PDF: ' + error);
    }
  };

  return (
    <div className="p-6 max-w-md mx-auto">
      <h2 className="text-2xl font-bold mb-4 text-right">اختبار PDF العربي</h2>
      <div className="space-y-4 text-right">
        <div>
          <strong>رقم الفاتورة:</strong> {testInvoice.invoice_number}
        </div>
        <div>
          <strong>العميل:</strong> {testInvoice.customers.name}
        </div>
        <div>
          <strong>المجموع:</strong> {testInvoice.total_amount.toFixed(2)} دج
        </div>
        <div>
          <strong>التاريخ:</strong> {new Date(testInvoice.invoice_date).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit', 
            year: 'numeric'
          })}
        </div>
      </div>
      
      <Button 
        onClick={handleGeneratePDF}
        className="w-full mt-6"
        size="lg"
      >
        إنشاء PDF مع النصوص العربية
      </Button>
      
      <div className="mt-4 text-sm text-gray-600 text-right">
        <p>هذا الاختبار سيقوم بإنشاء PDF يحتوي على:</p>
        <ul className="list-disc list-inside mt-2 space-y-1">
          <li>نصوص عربية صحيحة</li>
          <li>تاريخ بالتنسيق الفرنسي</li>
          <li>عملة الدينار الجزائري (دج)</li>
          <li>تخطيط من اليمين إلى اليسار</li>
        </ul>
      </div>
    </div>
  );
};

export default PDFTest;
