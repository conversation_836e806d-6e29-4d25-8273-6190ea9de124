
import { Layout } from "@/components/Layout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Plus, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Edit, 
  Trash2,
  MoreVertical,
  FileText,
  Calendar,
  DollarSign
} from "lucide-react";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { AddInvoiceDialog } from "@/components/AddInvoiceDialog";
import { ViewInvoiceDialog } from "@/components/ViewInvoiceDialog";
import { EditInvoiceDialog } from "@/components/EditInvoiceDialog";

interface Invoice {
  id: string;
  invoice_number: string;
  customer_id: string;
  invoice_date: string;
  due_date: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string | null;
  customers?: {
    name: string;
  };
  invoice_items?: Array<{
    quantity: number;
  }>;
}

const getStatusBadge = (status: string) => {
  const statusConfig = {
    paid: { label: "مدفوع", variant: "default", className: "bg-green-100 text-green-800" },
    partial: { label: "مدفوع جزئياً", variant: "secondary", className: "bg-yellow-100 text-yellow-800" },
    unpaid: { label: "غير مدفوع", variant: "destructive", className: "bg-red-100 text-red-800" },
  };
  
  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.unpaid;
  return (
    <Badge variant={config.variant as any} className={config.className}>
      {config.label}
    </Badge>
  );
};

const Invoices = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const { toast } = useToast();

  // جلب الفواتير من قاعدة البيانات
  const { data: invoices = [], isLoading, refetch } = useQuery({
    queryKey: ['invoices'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('invoices')
        .select(`
          *,
          customers (name),
          invoice_items (quantity)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Invoice[];
    }
  });

  const filteredInvoices = invoices.filter(invoice => 
    (filterStatus === "all" || invoice.payment_status === filterStatus) &&
    (invoice.customers?.name.includes(searchTerm) || invoice.invoice_number.includes(searchTerm))
  );

  const handleViewInvoice = (invoiceId: string) => {
    setSelectedInvoiceId(invoiceId);
    setIsViewDialogOpen(true);
  };

  const handleEditInvoice = (invoiceId: string) => {
    setSelectedInvoiceId(invoiceId);
    setIsEditDialogOpen(true);
  };

  const handleDownloadInvoice = async (invoiceId: string) => {
    try {
      // جلب بيانات الفاتورة كاملة
      const { data: invoice, error } = await supabase
        .from('invoices')
        .select(`
          *,
          customers (name, phone, email),
          invoice_items (
            *,
            products (name)
          )
        `)
        .eq('id', invoiceId)
        .single();

      if (error) throw error;

      // استدعاء وظيفة تحميل ملف PDF
      const { downloadInvoicePDF } = await import('@/utils/pdf-generator');
      const result = await downloadInvoicePDF(invoice);

      if (result) {
        toast({
          title: "تم التحميل",
          description: "تم تحميل ملف الفاتورة بنجاح",
        });
      } else {
        throw new Error("فشلت عملية إنشاء ملف PDF");
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ في تحميل الفاتورة",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (invoiceId: string) => {
    if (!confirm("هل أنت متأكد من حذف هذه الفاتورة؟")) return;

    try {
      // حذف عناصر الفاتورة أولاً
      await supabase.from('invoice_items').delete().eq('invoice_id', invoiceId);

      // ثم حذف الفاتورة
      const { error } = await supabase.from('invoices').delete().eq('id', invoiceId);

      if (error) throw error;

      toast({
        title: "تم الحذف",
        description: "تم حذف الفاتورة بنجاح",
      });

      refetch();
    } catch (error) {
      console.error('Error deleting invoice:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ في حذف الفاتورة",
        variant: "destructive",
      });
    }
  };

  const totalAmount = invoices.reduce((sum, invoice) => sum + invoice.total_amount, 0);
  const paidAmount = invoices.reduce((sum, invoice) => sum + invoice.paid_amount, 0);
  const unpaidAmount = invoices
    .filter(invoice => invoice.payment_status === "unpaid")
    .reduce((sum, invoice) => sum + invoice.remaining_amount, 0);

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">جاري التحميل...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الفواتير</h1>
            <p className="text-gray-600 mt-1">إدارة وتتبع جميع الفواتير</p>
          </div>
          <Button className="gap-2" onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="w-4 h-4" />
            فاتورة جديدة
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-600 text-sm font-medium">إجمالي الفواتير</p>
                  <p className="text-2xl font-bold text-blue-900">{invoices.length}</p>
                </div>
                <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                  <FileText className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-600 text-sm font-medium">المبلغ المدفوع</p>
                  <p className="text-2xl font-bold text-green-900">{paidAmount.toLocaleString()} ريال</p>
                </div>
                <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-600 text-sm font-medium">المبلغ غير المدفوع</p>
                  <p className="text-2xl font-bold text-yellow-900">{unpaidAmount.toLocaleString()} ريال</p>
                </div>
                <div className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                  <Calendar className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-600 text-sm font-medium">إجمالي المبلغ</p>
                  <p className="text-2xl font-bold text-purple-900">{totalAmount.toLocaleString()} ريال</p>
                </div>
                <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="flex gap-4 items-center w-full md:w-auto">
                <div className="relative flex-1 md:w-80">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="البحث في الفواتير..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="gap-2">
                      <Filter className="w-4 h-4" />
                      تصفية
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setFilterStatus("all")}>
                      جميع الفواتير
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilterStatus("paid")}>
                      مدفوع
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilterStatus("partial")}>
                      مدفوع جزئياً
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilterStatus("unpaid")}>
                      غير مدفوع
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <Button variant="outline" className="gap-2">
                <Download className="w-4 h-4" />
                تصدير
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Invoices Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة الفواتير</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">رقم الفاتورة</TableHead>
                  <TableHead className="text-right">اسم العميل</TableHead>
                  <TableHead className="text-right">تاريخ الإصدار</TableHead>
                  <TableHead className="text-right">تاريخ الاستحقاق</TableHead>
                  <TableHead className="text-right">المبلغ الإجمالي</TableHead>
                  <TableHead className="text-right">المبلغ المدفوع</TableHead>
                  <TableHead className="text-right">المتبقي</TableHead>
                  <TableHead className="text-right">عدد الأصناف</TableHead>
                  <TableHead className="text-right">الحالة</TableHead>
                  <TableHead className="text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInvoices.map((invoice) => (
                  <TableRow key={invoice.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{invoice.invoice_number}</TableCell>
                    <TableCell>{invoice.customers?.name || "غير محدد"}</TableCell>
                    <TableCell>{new Date(invoice.invoice_date).toLocaleDateString('ar-SA')}</TableCell>
                    <TableCell>
                      {invoice.due_date ? new Date(invoice.due_date).toLocaleDateString('ar-SA') : "غير محدد"}
                    </TableCell>
                    <TableCell className="font-semibold">{invoice.total_amount.toLocaleString()} ريال</TableCell>
                    <TableCell className="text-green-600">{invoice.paid_amount.toLocaleString()} ريال</TableCell>
                    <TableCell className="text-red-600">{invoice.remaining_amount.toLocaleString()} ريال</TableCell>
                    <TableCell>{invoice.invoice_items?.length || 0}</TableCell>
                    <TableCell>{getStatusBadge(invoice.payment_status)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem
                            className="gap-2"
                            onClick={() => handleViewInvoice(invoice.id)}
                          >
                            <Eye className="w-4 h-4" />
                            عرض
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="gap-2"
                            onClick={() => handleEditInvoice(invoice.id)}
                          >
                            <Edit className="w-4 h-4" />
                            تعديل
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="gap-2"
                            onClick={() => handleDownloadInvoice(invoice.id)}
                          >
                            <Download className="w-4 h-4" />
                            تحميل PDF
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="gap-2 text-red-600"
                            onClick={() => handleDelete(invoice.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                            حذف
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredInvoices.length === 0 && (
              <div className="text-center py-8">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد فواتير</h3>
                <p className="text-gray-500">
                  {searchTerm || filterStatus !== "all" ? "لم يتم العثور على فواتير مطابقة للبحث" : "ابدأ بإضافة أول فاتورة لك"}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Add Invoice Dialog */}
        <AddInvoiceDialog
          open={isAddDialogOpen}
          onOpenChange={setIsAddDialogOpen}
          onInvoiceAdded={refetch}
        />

        {/* View Invoice Dialog */}
        <ViewInvoiceDialog
          open={isViewDialogOpen}
          onOpenChange={setIsViewDialogOpen}
          invoiceId={selectedInvoiceId}
        />

        {/* Edit Invoice Dialog */}
        <EditInvoiceDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          invoiceId={selectedInvoiceId}
          onInvoiceUpdated={refetch}
        />
      </div>
    </Layout>
  );
};

export default Invoices;
