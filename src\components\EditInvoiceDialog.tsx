import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Plus, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import { formatCurrency } from "@/utils/currency";

interface InvoiceItem {
  id?: string;
  product_id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  is_new?: boolean; // لتمييز العناصر الجديدة
}

interface EditInvoiceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoiceId: string | null;
}

interface Invoice {
  id: string;
  invoice_number: string;
  customer_id: string;
  invoice_date: string;
  due_date: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string | null;
  notes: string | null;
  type: string;
}

export function EditInvoiceDialog({
  open,
  onOpenChange,
  invoiceId
}: EditInvoiceDialogProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState<Partial<Invoice>>({});
  const [items, setItems] = useState<InvoiceItem[]>([]);
  const [selectedProduct, setSelectedProduct] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [removedItems, setRemovedItems] = useState<string[]>([]);

  // جلب العملاء
  const { data: customers } = useQuery({
    queryKey: ['customers'],
    queryFn: async () => {
      const { data, error } = await supabase.from('customers').select('*');
      if (error) throw error;
      return data;
    }
  });

  // جلب المنتجات
  const { data: products } = useQuery({
    queryKey: ['products'],
    queryFn: async () => {
      const { data, error } = await supabase.from('products').select('*');
      if (error) throw error;
      return data;
    }
  });

  // إعادة تعيين الحالة عند إغلاق الحوار
  useEffect(() => {
    if (!open) {
      setFormData({});
      setItems([]);
      setSelectedProduct("");
      setQuantity(1);
      setRemovedItems([]);
      setIsSaving(false);
      setIsLoading(false);
    }
  }, [open]);

  // جلب بيانات الفاتورة عند فتح الحوار
  useEffect(() => {
    const fetchInvoiceData = async () => {
      if (!open || !invoiceId) return;
      
      setIsLoading(true);
      try {
        // جلب بيانات الفاتورة
        const { data: invoice, error: invoiceError } = await supabase
          .from('invoices')
          .select('*')
          .eq('id', invoiceId)
          .single();

        if (invoiceError) throw invoiceError;
        
        // جلب عناصر الفاتورة
        const { data: invoiceItems, error: itemsError } = await supabase
          .from('invoice_items')
          .select(`
            *,
            products (name, selling_price)
          `)
          .eq('invoice_id', invoiceId);

        if (itemsError) throw itemsError;
        
        // تحديث حالة المكون
        setFormData({
          ...invoice,
          invoice_date: invoice.invoice_date.split('T')[0],
          due_date: invoice.due_date ? invoice.due_date.split('T')[0] : '',
        });
        
        setItems(
          invoiceItems.map(item => ({
            id: item.id,
            product_id: item.product_id,
            product_name: item.products?.name || '',
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.total_price
          }))
        );
        
      } catch (error) {
        console.error('Error fetching invoice data:', error);
        toast({
          title: "خطأ",
          description: "حدث خطأ في جلب بيانات الفاتورة",
          variant: "destructive",
        });
        onOpenChange(false);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoiceData();
  }, [open, invoiceId, toast, onOpenChange]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const addItem = () => {
    const product = products?.find(p => p.id === selectedProduct);
    if (!product || quantity <= 0) return;

    const newItem: InvoiceItem = {
      product_id: product.id,
      product_name: product.name,
      quantity,
      unit_price: product.selling_price,
      total_price: quantity * product.selling_price,
      is_new: true
    };

    setItems([...items, newItem]);
    setSelectedProduct("");
    setQuantity(1);
  };

  const removeItem = (index: number) => {
    const itemToRemove = items[index];
    
    // إذا كان العنصر موجود بالفعل في قاعدة البيانات، احفظه للحذف لاحقًا
    if (itemToRemove.id && !itemToRemove.is_new) {
      setRemovedItems([...removedItems, itemToRemove.id]);
    }
    
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItemQuantity = (index: number, newQuantity: number) => {
    if (newQuantity <= 0) return;
    
    const updatedItems = [...items];
    updatedItems[index].quantity = newQuantity;
    updatedItems[index].total_price = newQuantity * updatedItems[index].unit_price;
    
    setItems(updatedItems);
  };

  const getTotalAmount = () => {
    return items.reduce((sum, item) => sum + item.total_price, 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.invoice_number || !formData.customer_id || items.length === 0) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة وإضافة منتج واحد على الأقل",
        variant: "destructive",
      });
      return;
    }
    
    setIsSaving(true);
    
    try {
      // 1. تحديث بيانات الفاتورة
      const totalAmount = getTotalAmount();
      const remainingAmount = totalAmount - (formData.paid_amount || 0);
      
      // تحديد حالة الدفع بناءً على المبلغ المتبقي
      let paymentStatus = 'unpaid';
      if (remainingAmount <= 0) {
        paymentStatus = 'paid';
      } else if (formData.paid_amount && formData.paid_amount > 0) {
        paymentStatus = 'partial';
      }
      
      const invoiceData = {
        ...formData,
        total_amount: totalAmount,
        remaining_amount: remainingAmount,
        payment_status: paymentStatus
      };
      
      const { error: invoiceError } = await supabase
        .from('invoices')
        .update(invoiceData)
        .eq('id', invoiceId);
      
      if (invoiceError) throw invoiceError;
      
      // 2. التعامل مع العناصر المحذوفة
      if (removedItems.length > 0) {
        const { error: deleteError } = await supabase
          .from('invoice_items')
          .delete()
          .in('id', removedItems);
        
        if (deleteError) throw deleteError;
      }
      
      // 3. تحديث العناصر الموجودة وإضافة العناصر الجديدة
      for (const item of items) {
        const itemData = {
          invoice_id: invoiceId,
          product_id: item.product_id,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.total_price
        };
        
        if (item.id && !item.is_new) {
          // تحديث عنصر موجود
          const { error: updateError } = await supabase
            .from('invoice_items')
            .update(itemData)
            .eq('id', item.id);
          
          if (updateError) throw updateError;
        } else {
          // إضافة عنصر جديد
          const { error: insertError } = await supabase
            .from('invoice_items')
            .insert(itemData);
          
          if (insertError) throw insertError;
        }
      }
      
      toast({
        title: "تم بنجاح ✅",
        description: "تم تحديث الفاتورة بنجاح. اضغط F5 أو حدث الصفحة لرؤية التغييرات.",
        duration: 8000,
      });

      // إغلاق الحوار فقط
      onOpenChange(false);
      
    } catch (error: unknown) {
      console.error('Error updating invoice:', error);
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      toast({
        title: "خطأ",
        description: `حدث خطأ في تحديث الفاتورة: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      // التأكد من إعادة تعيين حالة الحفظ
      setTimeout(() => {
        setIsSaving(false);
      }, 100);
    }
  };

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>تحميل...</DialogTitle>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>تعديل الفاتورة #{formData.invoice_number}</DialogTitle>
          <DialogDescription>قم بتعديل بيانات الفاتورة والمنتجات</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* رقم الفاتورة */}
            <div className="space-y-2">
              <Label htmlFor="invoice_number">رقم الفاتورة</Label>
              <Input
                id="invoice_number"
                name="invoice_number"
                value={formData.invoice_number || ""}
                onChange={handleInputChange}
                required
                readOnly
              />
            </div>
            
            {/* العميل */}
            <div className="space-y-2">
              <Label htmlFor="customer_id">العميل</Label>
              <Select
                value={formData.customer_id}
                onValueChange={(value) => handleSelectChange("customer_id", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر العميل" />
                </SelectTrigger>
                <SelectContent>
                  {customers?.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* تاريخ الفاتورة */}
            <div className="space-y-2">
              <Label htmlFor="invoice_date">تاريخ الفاتورة</Label>
              <Input
                id="invoice_date"
                name="invoice_date"
                type="date"
                value={formData.invoice_date || ""}
                onChange={handleInputChange}
                required
              />
            </div>
            
            {/* تاريخ الاستحقاق */}
            <div className="space-y-2">
              <Label htmlFor="due_date">تاريخ الاستحقاق</Label>
              <Input
                id="due_date"
                name="due_date"
                type="date"
                value={formData.due_date || ""}
                onChange={handleInputChange}
              />
            </div>
            
            {/* طريقة الدفع */}
            <div className="space-y-2">
              <Label htmlFor="payment_method">طريقة الدفع</Label>              <Select
                value={formData.payment_method || 'none'}
                onValueChange={(value) => handleSelectChange("payment_method", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر طريقة الدفع" />
                </SelectTrigger>                <SelectContent>
                  <SelectItem value="none">اختر طريقة الدفع</SelectItem>
                  <SelectItem value="cash">نقداً</SelectItem>
                  <SelectItem value="card">بطاقة ائتمان</SelectItem>
                  <SelectItem value="bank">تحويل بنكي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* المبلغ المدفوع */}
            <div className="space-y-2">
              <Label htmlFor="paid_amount">المبلغ المدفوع</Label>
              <Input
                id="paid_amount"
                name="paid_amount"
                type="number"
                value={formData.paid_amount || ""}
                onChange={handleInputChange}
              />
            </div>
            
            {/* الملاحظات */}
            <div className="space-y-2 col-span-2">
              <Label htmlFor="notes">ملاحظات</Label>
              <Textarea
                id="notes"
                name="notes"
                rows={3}
                value={formData.notes || ""}
                onChange={handleInputChange}
                placeholder="أضف ملاحظات عن الفاتورة هنا..."
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="bg-muted/20 p-4 rounded-lg">
              <h3 className="font-medium mb-3">إضافة منتج</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div className="md:col-span-2">
                  <Label htmlFor="product">المنتج</Label>
                  <Select
                    value={selectedProduct}
                    onValueChange={setSelectedProduct}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر منتج" />
                    </SelectTrigger>
                    <SelectContent>
                      {products?.map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          {product.name} - {formatCurrency(product.selling_price)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="quantity">الكمية</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                  />
                </div>
                <div>
                  <Button
                    type="button"
                    onClick={addItem}
                    className="w-full"
                    disabled={!selectedProduct}
                  >
                    <Plus className="ml-2 h-4 w-4" /> إضافة
                  </Button>
                </div>
              </div>
            </div>

            <div className="rounded-lg border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">المنتج</TableHead>
                    <TableHead className="text-right">الكمية</TableHead>
                    <TableHead className="text-right">السعر</TableHead>
                    <TableHead className="text-right">المجموع</TableHead>
                    <TableHead className="text-right w-[80px]">إجراء</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {items.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                        لم يتم إضافة منتجات بعد
                      </TableCell>
                    </TableRow>
                  ) : (
                    items.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{item.product_name}</TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) => updateItemQuantity(index, parseInt(e.target.value) || 1)}
                            className="w-20"
                          />
                        </TableCell>
                        <TableCell>{formatCurrency(item.unit_price)}</TableCell>
                        <TableCell>{formatCurrency(item.total_price)}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeItem(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {items.length > 0 && (
              <div className="flex justify-end">
                <div className="w-64">
                  <div className="flex justify-between py-2 text-lg font-medium">
                    <span>المجموع:</span>
                    <span>{formatCurrency(getTotalAmount())}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isSaving || items.length === 0}
              className="gap-2"
            >
              {isSaving ? "جاري الحفظ..." : "حفظ التغييرات"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
