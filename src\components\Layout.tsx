
import React from 'react';
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from './AppSidebar';
import { Header } from './Header';
import './sidebar-modern.css'; // استيراد التنسيق المخصص

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <SidebarProvider>
      <div className="sidebar-layout bg-gray-50">
        <div className="main-content">
          <Header />
          <main>
            {children}
          </main>
        </div>
        <div className="sidebar-container">
          <AppSidebar />
        </div>
      </div>
    </SidebarProvider>
  );
}
