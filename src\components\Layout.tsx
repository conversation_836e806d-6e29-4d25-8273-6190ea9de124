
import React from 'react';
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from './AppSidebar';
import { Header } from './Header';
import './sidebar-modern.css';

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <SidebarProvider>
      <div className="min-h-screen bg-gray-50" dir="rtl" style={{ display: 'flex' }}>
        {/* المحتوى الرئيسي */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column', marginRight: '320px' }}>
          <Header />
          <main style={{ flex: 1, padding: '1.5rem', overflowY: 'auto' }}>
            {children}
          </main>
        </div>

        {/* الشريط الجانبي */}
        <div style={{
          position: 'fixed',
          top: 0,
          right: 0,
          width: '320px',
          height: '100vh',
          zIndex: 50,
          backgroundColor: 'white',
          borderLeft: '1px solid #e5e7eb'
        }}>
          <AppSidebar />
        </div>
      </div>
    </SidebarProvider>
  );
}
