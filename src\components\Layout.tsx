
import React from 'react';
import { Header } from './Header';
import { SimpleSidebar } from './SimpleSidebar';

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* الشريط الجانبي البسيط */}
      <SimpleSidebar />

      {/* المحتوى الرئيسي */}
      <div style={{
        marginRight: '280px', // تقليل العرض قليلاً
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <Header />
        <main style={{
          flex: 1,
          padding: '2rem', // زيادة المساحة
          backgroundColor: '#f8fafc',
          maxWidth: '100%',
          overflow: 'hidden' // منع التمرير الأفقي
        }}>
          {children}
        </main>
      </div>
    </div>
  );
}
