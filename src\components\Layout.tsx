
import React from 'react';
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from './AppSidebar';
import { Header } from './Header';
import './sidebar-modern.css';

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* الشريط الجانبي الثابت */}
      <div style={{
        position: 'fixed',
        top: 0,
        right: 0,
        width: '320px',
        height: '100vh',
        zIndex: 1000,
        backgroundColor: 'white',
        borderLeft: '1px solid #e5e7eb',
        overflowY: 'auto'
      }}>
        <SidebarProvider>
          <AppSidebar />
        </SidebarProvider>
      </div>

      {/* المحتوى الرئيسي */}
      <div style={{
        marginRight: '320px',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <Header />
        <main style={{
          flex: 1,
          padding: '1.5rem',
          backgroundColor: '#f9fafb'
        }}>
          {children}
        </main>
      </div>
    </div>
  );
}
