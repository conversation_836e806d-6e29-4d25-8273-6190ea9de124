import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  ShoppingCart, 
  Users, 
  Package,
  Calendar,
  Download,
  Eye,
  AlertTriangle
} from 'lucide-react';
import { Invoice, Customer, Supplier, Product } from '@/types';
import { formatCurrency } from '@/utils/currency';

interface IntelligentReportsProps {
  invoices: Invoice[];
  customers: Customer[];
  suppliers: Supplier[];
  products: Product[];
}

interface ReportData {
  dailySales: Array<{ date: string; sales: number; purchases: number; profit: number }>;
  topCustomers: Array<{ name: string; total: number; count: number }>;
  topSuppliers: Array<{ name: string; total: number; count: number }>;
  topProducts: Array<{ name: string; quantity: number; revenue: number }>;
  paymentStatus: Array<{ name: string; value: number; color: string }>;
  monthlyTrends: Array<{ month: string; sales: number; purchases: number; profit: number }>;
}

const IntelligentReports: React.FC<IntelligentReportsProps> = ({
  invoices,
  customers,
  suppliers,
  products
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('30'); // آخر 30 يوم
  const [selectedReport, setSelectedReport] = useState('overview');

  // حساب البيانات للتقارير
  const reportData = useMemo((): ReportData => {
    const now = new Date();
    const periodDays = parseInt(selectedPeriod);
    const startDate = new Date(now.getTime() - (periodDays * 24 * 60 * 60 * 1000));

    // تصفية الفواتير حسب الفترة المحددة
    const filteredInvoices = invoices.filter(invoice => 
      new Date(invoice.invoice_date) >= startDate
    );

    // المبيعات اليومية
    const dailySalesMap = new Map<string, { sales: number; purchases: number; profit: number }>();
    
    filteredInvoices.forEach(invoice => {
      const date = invoice.invoice_date;
      const current = dailySalesMap.get(date) || { sales: 0, purchases: 0, profit: 0 };
      
      if (invoice.type === 'sales') {
        current.sales += invoice.total_amount;
        current.profit += invoice.total_amount * 0.3; // افتراض هامش ربح 30%
      } else if (invoice.type === 'purchase') {
        current.purchases += invoice.total_amount;
      }
      
      dailySalesMap.set(date, current);
    });

    const dailySales = Array.from(dailySalesMap.entries())
      .map(([date, data]) => ({
        date: new Date(date).toLocaleDateString('ar-SA'),
        ...data
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(-30); // آخر 30 يوم

    // أفضل العملاء
    const customerSales = new Map<string, { total: number; count: number }>();
    
    filteredInvoices
      .filter(invoice => invoice.type === 'sales' && invoice.customer_id)
      .forEach(invoice => {
        const current = customerSales.get(invoice.customer_id!) || { total: 0, count: 0 };
        current.total += invoice.total_amount;
        current.count += 1;
        customerSales.set(invoice.customer_id!, current);
      });

    const topCustomers = Array.from(customerSales.entries())
      .map(([customerId, data]) => ({
        name: customers.find(c => c.id === customerId)?.name || 'غير معروف',
        ...data
      }))
      .sort((a, b) => b.total - a.total)
      .slice(0, 10);

    // أفضل الموردين
    const supplierPurchases = new Map<string, { total: number; count: number }>();
    
    filteredInvoices
      .filter(invoice => invoice.type === 'purchase' && invoice.supplier_id)
      .forEach(invoice => {
        const current = supplierPurchases.get(invoice.supplier_id!) || { total: 0, count: 0 };
        current.total += invoice.total_amount;
        current.count += 1;
        supplierPurchases.set(invoice.supplier_id!, current);
      });

    const topSuppliers = Array.from(supplierPurchases.entries())
      .map(([supplierId, data]) => ({
        name: suppliers.find(s => s.id === supplierId)?.name || 'غير معروف',
        ...data
      }))
      .sort((a, b) => b.total - a.total)
      .slice(0, 10);

    // أفضل المنتجات
    const productSales = new Map<string, { quantity: number; revenue: number }>();
    
    filteredInvoices
      .filter(invoice => invoice.type === 'sales')
      .forEach(invoice => {
        invoice.items?.forEach(item => {
          const current = productSales.get(item.product_id) || { quantity: 0, revenue: 0 };
          current.quantity += item.quantity;
          current.revenue += item.total_price;
          productSales.set(item.product_id, current);
        });
      });

    const topProducts = Array.from(productSales.entries())
      .map(([productId, data]) => ({
        name: products.find(p => p.id === productId)?.name || 'غير معروف',
        ...data
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // حالة الدفع
    const paymentStatusCounts = {
      paid: 0,
      partial: 0,
      unpaid: 0
    };

    filteredInvoices.forEach(invoice => {
      paymentStatusCounts[invoice.payment_status]++;
    });

    const paymentStatus = [
      { name: 'مدفوع', value: paymentStatusCounts.paid, color: '#10B981' },
      { name: 'جزئي', value: paymentStatusCounts.partial, color: '#F59E0B' },
      { name: 'غير مدفوع', value: paymentStatusCounts.unpaid, color: '#EF4444' }
    ];

    // الاتجاهات الشهرية
    const monthlyData = new Map<string, { sales: number; purchases: number; profit: number }>();
    
    filteredInvoices.forEach(invoice => {
      const month = new Date(invoice.invoice_date).toLocaleDateString('ar-SA', { 
        year: 'numeric', 
        month: 'short' 
      });
      const current = monthlyData.get(month) || { sales: 0, purchases: 0, profit: 0 };
      
      if (invoice.type === 'sales') {
        current.sales += invoice.total_amount;
        current.profit += invoice.total_amount * 0.3;
      } else if (invoice.type === 'purchase') {
        current.purchases += invoice.total_amount;
      }
      
      monthlyData.set(month, current);
    });

    const monthlyTrends = Array.from(monthlyData.entries())
      .map(([month, data]) => ({ month, ...data }))
      .sort((a, b) => new Date(a.month).getTime() - new Date(b.month).getTime());

    return {
      dailySales,
      topCustomers,
      topSuppliers,
      topProducts,
      paymentStatus,
      monthlyTrends
    };
  }, [invoices, customers, suppliers, products, selectedPeriod]);

  // حساب الإحصائيات الرئيسية
  const stats = useMemo(() => {
    const now = new Date();
    const periodDays = parseInt(selectedPeriod);
    const startDate = new Date(now.getTime() - (periodDays * 24 * 60 * 60 * 1000));

    const filteredInvoices = invoices.filter(invoice => 
      new Date(invoice.invoice_date) >= startDate
    );

    const totalSales = filteredInvoices
      .filter(i => i.type === 'sales')
      .reduce((sum, i) => sum + i.total_amount, 0);

    const totalPurchases = filteredInvoices
      .filter(i => i.type === 'purchase')
      .reduce((sum, i) => sum + i.total_amount, 0);

    const totalProfit = totalSales * 0.3; // افتراض هامش ربح 30%

    const paidAmount = filteredInvoices
      .reduce((sum, i) => sum + i.paid_amount, 0);

    const pendingAmount = filteredInvoices
      .reduce((sum, i) => sum + i.remaining_amount, 0);

    return {
      totalSales,
      totalPurchases,
      totalProfit,
      paidAmount,
      pendingAmount,
      invoicesCount: filteredInvoices.length,
      customersCount: new Set(filteredInvoices.map(i => i.customer_id).filter(Boolean)).size,
      suppliersCount: new Set(filteredInvoices.map(i => i.supplier_id).filter(Boolean)).size
    };
  }, [invoices, selectedPeriod]);

  const COLORS = ['#10B981', '#F59E0B', '#EF4444', '#3B82F6', '#8B5CF6'];

  return (
    <div className="space-y-6 p-6" dir="rtl">
      {/* عناصر التحكم */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <BarChart className="w-5 h-5" />
              التقارير الذكية
            </span>
            <div className="flex gap-4">
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">آخر 7 أيام</SelectItem>
                  <SelectItem value="30">آخر 30 يوم</SelectItem>
                  <SelectItem value="90">آخر 3 أشهر</SelectItem>
                  <SelectItem value="365">آخر سنة</SelectItem>
                </SelectContent>
              </Select>
              
              <Button variant="outline">
                <Download className="w-4 h-4 ml-2" />
                تصدير
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المبيعات</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(stats.totalSales)}
                </p>
                <p className="text-xs text-gray-500">
                  {stats.invoicesCount} فاتورة
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المشتريات</p>
                <p className="text-2xl font-bold text-orange-600">
                  {formatCurrency(stats.totalPurchases)}
                </p>
                <p className="text-xs text-gray-500">
                  {stats.suppliersCount} مورد
                </p>
              </div>
              <ShoppingCart className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الربح المتوقع</p>
                <p className="text-2xl font-bold text-blue-600">
                  {formatCurrency(stats.totalProfit)}
                </p>
                <p className="text-xs text-gray-500">
                  هامش 30%
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">المبالغ المعلقة</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(stats.pendingAmount)}
                </p>
                <p className="text-xs text-gray-500">
                  {stats.customersCount} عميل
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* التبويبات */}
      <Tabs value={selectedReport} onValueChange={setSelectedReport}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="customers">العملاء</TabsTrigger>
          <TabsTrigger value="suppliers">الموردين</TabsTrigger>
          <TabsTrigger value="products">المنتجات</TabsTrigger>
        </TabsList>

        {/* نظرة عامة */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* المبيعات اليومية */}
            <Card>
              <CardHeader>
                <CardTitle>المبيعات اليومية</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={reportData.dailySales}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Area type="monotone" dataKey="sales" stackId="1" stroke="#10B981" fill="#10B981" />
                    <Area type="monotone" dataKey="purchases" stackId="1" stroke="#F59E0B" fill="#F59E0B" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* حالة الدفع */}
            <Card>
              <CardHeader>
                <CardTitle>حالة الدفع</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={reportData.paymentStatus}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {reportData.paymentStatus.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* الاتجاهات الشهرية */}
          <Card>
            <CardHeader>
              <CardTitle>الاتجاهات الشهرية</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={reportData.monthlyTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  <Line type="monotone" dataKey="sales" stroke="#10B981" strokeWidth={2} />
                  <Line type="monotone" dataKey="purchases" stroke="#F59E0B" strokeWidth={2} />
                  <Line type="monotone" dataKey="profit" stroke="#3B82F6" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* تقرير العملاء */}
        <TabsContent value="customers">
          <Card>
            <CardHeader>
              <CardTitle>أفضل العملاء</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.topCustomers.map((customer, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div>
                        <div className="font-medium">{customer.name}</div>
                        <div className="text-sm text-gray-500">
                          {customer.count} فاتورة
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-green-600">
                        {formatCurrency(customer.total)}
                      </div>
                      <div className="text-sm text-gray-500">
                        متوسط: {formatCurrency(customer.total / customer.count)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* تقرير الموردين */}
        <TabsContent value="suppliers">
          <Card>
            <CardHeader>
              <CardTitle>أفضل الموردين</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.topSuppliers.map((supplier, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div>
                        <div className="font-medium">{supplier.name}</div>
                        <div className="text-sm text-gray-500">
                          {supplier.count} فاتورة
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-orange-600">
                        {formatCurrency(supplier.total)}
                      </div>
                      <div className="text-sm text-gray-500">
                        متوسط: {formatCurrency(supplier.total / supplier.count)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* تقرير المنتجات */}
        <TabsContent value="products">
          <Card>
            <CardHeader>
              <CardTitle>أفضل المنتجات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.topProducts.map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div>
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-gray-500">
                          {product.quantity} قطعة مباعة
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-blue-600">
                        {formatCurrency(product.revenue)}
                      </div>
                      <div className="text-sm text-gray-500">
                        متوسط: {formatCurrency(product.revenue / product.quantity)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default IntelligentReports;
